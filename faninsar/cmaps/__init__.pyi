from pathlib import Path

from .enhanced_colormap import EnhancedLinearSegmentedColormap

class ColormapLoader:
    data_dir: Path
    names: list[str]

    def __init__(self, data_dir: str | Path) -> None: ...
    def __getattr__(self, name: str) -> EnhancedLinearSegmentedColormap: ...
    def __dir__(self) -> list[str]: ...
    @property
    def __all__(self) -> list[str]: ...

class GMTColormaps(ColormapLoader):
    abyss: EnhancedLinearSegmentedColormap
    abyss_r: EnhancedLinearSegmentedColormap
    bathy: EnhancedLinearSegmentedColormap
    bathy_r: EnhancedLinearSegmentedColormap
    cool: EnhancedLinearSegmentedColormap
    cool_r: EnhancedLinearSegmentedColormap
    copper: EnhancedLinearSegmentedColormap
    copper_r: EnhancedLinearSegmentedColormap
    cubhelix: EnhancedLinearSegmentedColormap
    cubhelix_r: EnhancedLinearSegmentedColormap
    cyclic: EnhancedLinearSegmentedColormap
    cyclic_r: EnhancedLinearSegmentedColormap
    dem1: EnhancedLinearSegmentedColormap
    dem1_r: EnhancedLinearSegmentedColormap
    dem2: EnhancedLinearSegmentedColormap
    dem2_r: EnhancedLinearSegmentedColormap
    dem3: EnhancedLinearSegmentedColormap
    dem3_r: EnhancedLinearSegmentedColormap
    dem4: EnhancedLinearSegmentedColormap
    dem4_r: EnhancedLinearSegmentedColormap
    drywet: EnhancedLinearSegmentedColormap
    drywet_r: EnhancedLinearSegmentedColormap
    earth: EnhancedLinearSegmentedColormap
    earth_r: EnhancedLinearSegmentedColormap
    elevation: EnhancedLinearSegmentedColormap
    elevation_r: EnhancedLinearSegmentedColormap
    etopo1: EnhancedLinearSegmentedColormap
    etopo1_r: EnhancedLinearSegmentedColormap
    geo: EnhancedLinearSegmentedColormap
    geo_r: EnhancedLinearSegmentedColormap
    globe: EnhancedLinearSegmentedColormap
    globe_r: EnhancedLinearSegmentedColormap
    gray: EnhancedLinearSegmentedColormap
    gray_r: EnhancedLinearSegmentedColormap
    haxby: EnhancedLinearSegmentedColormap
    haxby_r: EnhancedLinearSegmentedColormap
    hot: EnhancedLinearSegmentedColormap
    hot_r: EnhancedLinearSegmentedColormap
    inferno: EnhancedLinearSegmentedColormap
    inferno_r: EnhancedLinearSegmentedColormap
    jet: EnhancedLinearSegmentedColormap
    jet_r: EnhancedLinearSegmentedColormap
    magma: EnhancedLinearSegmentedColormap
    magma_r: EnhancedLinearSegmentedColormap
    nighttime: EnhancedLinearSegmentedColormap
    nighttime_r: EnhancedLinearSegmentedColormap
    no_green: EnhancedLinearSegmentedColormap
    no_green_r: EnhancedLinearSegmentedColormap
    ocean: EnhancedLinearSegmentedColormap
    ocean_r: EnhancedLinearSegmentedColormap
    plasma: EnhancedLinearSegmentedColormap
    plasma_r: EnhancedLinearSegmentedColormap
    polar: EnhancedLinearSegmentedColormap
    polar_r: EnhancedLinearSegmentedColormap
    rainbow: EnhancedLinearSegmentedColormap
    rainbow_r: EnhancedLinearSegmentedColormap
    red2green: EnhancedLinearSegmentedColormap
    red2green_r: EnhancedLinearSegmentedColormap
    relief: EnhancedLinearSegmentedColormap
    relief_r: EnhancedLinearSegmentedColormap
    seafloor: EnhancedLinearSegmentedColormap
    seafloor_r: EnhancedLinearSegmentedColormap
    sealand: EnhancedLinearSegmentedColormap
    sealand_r: EnhancedLinearSegmentedColormap
    seis: EnhancedLinearSegmentedColormap
    seis_r: EnhancedLinearSegmentedColormap
    split: EnhancedLinearSegmentedColormap
    split_r: EnhancedLinearSegmentedColormap
    srtm: EnhancedLinearSegmentedColormap
    srtm_r: EnhancedLinearSegmentedColormap
    terra: EnhancedLinearSegmentedColormap
    terra_r: EnhancedLinearSegmentedColormap
    topo: EnhancedLinearSegmentedColormap
    topo_r: EnhancedLinearSegmentedColormap
    turbo: EnhancedLinearSegmentedColormap
    turbo_r: EnhancedLinearSegmentedColormap
    viridis: EnhancedLinearSegmentedColormap
    viridis_r: EnhancedLinearSegmentedColormap
    world: EnhancedLinearSegmentedColormap
    world_r: EnhancedLinearSegmentedColormap
    wysiwyg: EnhancedLinearSegmentedColormap
    wysiwyg_r: EnhancedLinearSegmentedColormap

class SCMColormaps(ColormapLoader):
    version: str
    acton: EnhancedLinearSegmentedColormap
    acton_r: EnhancedLinearSegmentedColormap
    bam: EnhancedLinearSegmentedColormap
    bam_r: EnhancedLinearSegmentedColormap
    bamako: EnhancedLinearSegmentedColormap
    bamako_r: EnhancedLinearSegmentedColormap
    bamO: EnhancedLinearSegmentedColormap
    bamO_r: EnhancedLinearSegmentedColormap
    batlow: EnhancedLinearSegmentedColormap
    batlow_r: EnhancedLinearSegmentedColormap
    batlowK: EnhancedLinearSegmentedColormap
    batlowK_r: EnhancedLinearSegmentedColormap
    batlowW: EnhancedLinearSegmentedColormap
    batlowW_r: EnhancedLinearSegmentedColormap
    berlin: EnhancedLinearSegmentedColormap
    berlin_r: EnhancedLinearSegmentedColormap
    bilbao: EnhancedLinearSegmentedColormap
    bilbao_r: EnhancedLinearSegmentedColormap
    broc: EnhancedLinearSegmentedColormap
    broc_r: EnhancedLinearSegmentedColormap
    brocO: EnhancedLinearSegmentedColormap
    brocO_r: EnhancedLinearSegmentedColormap
    buda: EnhancedLinearSegmentedColormap
    buda_r: EnhancedLinearSegmentedColormap
    bukavu: EnhancedLinearSegmentedColormap
    bukavu_r: EnhancedLinearSegmentedColormap
    cork: EnhancedLinearSegmentedColormap
    cork_r: EnhancedLinearSegmentedColormap
    corkO: EnhancedLinearSegmentedColormap
    corkO_r: EnhancedLinearSegmentedColormap
    davos: EnhancedLinearSegmentedColormap
    davos_r: EnhancedLinearSegmentedColormap
    devon: EnhancedLinearSegmentedColormap
    devon_r: EnhancedLinearSegmentedColormap
    fes: EnhancedLinearSegmentedColormap
    fes_r: EnhancedLinearSegmentedColormap
    glasgow: EnhancedLinearSegmentedColormap
    glasgow_r: EnhancedLinearSegmentedColormap
    grayC: EnhancedLinearSegmentedColormap
    grayC_r: EnhancedLinearSegmentedColormap
    hawaii: EnhancedLinearSegmentedColormap
    hawaii_r: EnhancedLinearSegmentedColormap
    imola: EnhancedLinearSegmentedColormap
    imola_r: EnhancedLinearSegmentedColormap
    lajolla: EnhancedLinearSegmentedColormap
    lajolla_r: EnhancedLinearSegmentedColormap
    lapaz: EnhancedLinearSegmentedColormap
    lapaz_r: EnhancedLinearSegmentedColormap
    lipari: EnhancedLinearSegmentedColormap
    lipari_r: EnhancedLinearSegmentedColormap
    lisbon: EnhancedLinearSegmentedColormap
    lisbon_r: EnhancedLinearSegmentedColormap
    managua: EnhancedLinearSegmentedColormap
    managua_r: EnhancedLinearSegmentedColormap
    navia: EnhancedLinearSegmentedColormap
    navia_r: EnhancedLinearSegmentedColormap
    nuuk: EnhancedLinearSegmentedColormap
    nuuk_r: EnhancedLinearSegmentedColormap
    oleron: EnhancedLinearSegmentedColormap
    oleron_r: EnhancedLinearSegmentedColormap
    oslo: EnhancedLinearSegmentedColormap
    oslo_r: EnhancedLinearSegmentedColormap
    roma: EnhancedLinearSegmentedColormap
    roma_r: EnhancedLinearSegmentedColormap
    romaO: EnhancedLinearSegmentedColormap
    romaO_r: EnhancedLinearSegmentedColormap
    tofino: EnhancedLinearSegmentedColormap
    tofino_r: EnhancedLinearSegmentedColormap
    tokyo: EnhancedLinearSegmentedColormap
    tokyo_r: EnhancedLinearSegmentedColormap
    turku: EnhancedLinearSegmentedColormap
    turku_r: EnhancedLinearSegmentedColormap
    vanimo: EnhancedLinearSegmentedColormap
    vanimo_r: EnhancedLinearSegmentedColormap
    vik: EnhancedLinearSegmentedColormap
    vik_r: EnhancedLinearSegmentedColormap
    vikO: EnhancedLinearSegmentedColormap
    vikO_r: EnhancedLinearSegmentedColormap

class CmoceanColormaps(ColormapLoader):
    algae: EnhancedLinearSegmentedColormap
    algae_r: EnhancedLinearSegmentedColormap
    amp: EnhancedLinearSegmentedColormap
    amp_r: EnhancedLinearSegmentedColormap
    balance: EnhancedLinearSegmentedColormap
    balance_r: EnhancedLinearSegmentedColormap
    curl: EnhancedLinearSegmentedColormap
    curl_r: EnhancedLinearSegmentedColormap
    deep: EnhancedLinearSegmentedColormap
    deep_r: EnhancedLinearSegmentedColormap
    delta: EnhancedLinearSegmentedColormap
    delta_r: EnhancedLinearSegmentedColormap
    dense: EnhancedLinearSegmentedColormap
    dense_r: EnhancedLinearSegmentedColormap
    diff: EnhancedLinearSegmentedColormap
    diff_r: EnhancedLinearSegmentedColormap
    gray: EnhancedLinearSegmentedColormap
    gray_r: EnhancedLinearSegmentedColormap
    haline: EnhancedLinearSegmentedColormap
    haline_r: EnhancedLinearSegmentedColormap
    ice: EnhancedLinearSegmentedColormap
    ice_r: EnhancedLinearSegmentedColormap
    matter: EnhancedLinearSegmentedColormap
    matter_r: EnhancedLinearSegmentedColormap
    oxy: EnhancedLinearSegmentedColormap
    oxy_r: EnhancedLinearSegmentedColormap
    phase: EnhancedLinearSegmentedColormap
    phase_r: EnhancedLinearSegmentedColormap
    rain: EnhancedLinearSegmentedColormap
    rain_r: EnhancedLinearSegmentedColormap
    solar: EnhancedLinearSegmentedColormap
    solar_r: EnhancedLinearSegmentedColormap
    speed: EnhancedLinearSegmentedColormap
    speed_r: EnhancedLinearSegmentedColormap
    tarn: EnhancedLinearSegmentedColormap
    tarn_r: EnhancedLinearSegmentedColormap
    tempo: EnhancedLinearSegmentedColormap
    tempo_r: EnhancedLinearSegmentedColormap
    thermal: EnhancedLinearSegmentedColormap
    thermal_r: EnhancedLinearSegmentedColormap
    topo: EnhancedLinearSegmentedColormap
    topo_r: EnhancedLinearSegmentedColormap
    turbid: EnhancedLinearSegmentedColormap
    turbid_r: EnhancedLinearSegmentedColormap

class ColorcetColormaps(ColormapLoader):
    bkr: EnhancedLinearSegmentedColormap
    bkr_r: EnhancedLinearSegmentedColormap
    bky: EnhancedLinearSegmentedColormap
    bky_r: EnhancedLinearSegmentedColormap
    bwy: EnhancedLinearSegmentedColormap
    bwy_r: EnhancedLinearSegmentedColormap
    cwr: EnhancedLinearSegmentedColormap
    cwr_r: EnhancedLinearSegmentedColormap
    colorwheel: EnhancedLinearSegmentedColormap
    colorwheel_r: EnhancedLinearSegmentedColormap
    coolwarm: EnhancedLinearSegmentedColormap
    coolwarm_r: EnhancedLinearSegmentedColormap
    gwv: EnhancedLinearSegmentedColormap
    gwv_r: EnhancedLinearSegmentedColormap
    bjy: EnhancedLinearSegmentedColormap
    bjy_r: EnhancedLinearSegmentedColormap
    isolum: EnhancedLinearSegmentedColormap
    isolum_r: EnhancedLinearSegmentedColormap
    bgy: EnhancedLinearSegmentedColormap
    bgy_r: EnhancedLinearSegmentedColormap
    bgyw: EnhancedLinearSegmentedColormap
    bgyw_r: EnhancedLinearSegmentedColormap
    kbc: EnhancedLinearSegmentedColormap
    kbc_r: EnhancedLinearSegmentedColormap
    blues: EnhancedLinearSegmentedColormap
    blues_r: EnhancedLinearSegmentedColormap
    bmw: EnhancedLinearSegmentedColormap
    bmw_r: EnhancedLinearSegmentedColormap
    bmy: EnhancedLinearSegmentedColormap
    bmy_r: EnhancedLinearSegmentedColormap
    kgy: EnhancedLinearSegmentedColormap
    kgy_r: EnhancedLinearSegmentedColormap
    gray: EnhancedLinearSegmentedColormap
    gray_r: EnhancedLinearSegmentedColormap
    dimgray: EnhancedLinearSegmentedColormap
    dimgray_r: EnhancedLinearSegmentedColormap
    fire: EnhancedLinearSegmentedColormap
    fire_r: EnhancedLinearSegmentedColormap
    kb: EnhancedLinearSegmentedColormap
    kb_r: EnhancedLinearSegmentedColormap
    kg: EnhancedLinearSegmentedColormap
    kg_r: EnhancedLinearSegmentedColormap
    kr: EnhancedLinearSegmentedColormap
    kr_r: EnhancedLinearSegmentedColormap
    rainbow: EnhancedLinearSegmentedColormap
    rainbow_r: EnhancedLinearSegmentedColormap

class MintpyColormaps(ColormapLoader):
    cmy: EnhancedLinearSegmentedColormap
    cmy_r: EnhancedLinearSegmentedColormap
    dismph: EnhancedLinearSegmentedColormap
    dismph_r: EnhancedLinearSegmentedColormap
    romanian: EnhancedLinearSegmentedColormap
    romanian_r: EnhancedLinearSegmentedColormap

class Cmaps(
    GMTColormaps, SCMColormaps, CmoceanColormaps, ColorcetColormaps, MintpyColormaps
):
    GMT: GMTColormaps
    SCM: SCMColormaps
    cmocean: CmoceanColormaps
    colorcet: ColorcetColormaps
    mintpy: MintpyColormaps

    def __getattr__(self, name: str) -> EnhancedLinearSegmentedColormap: ...
    def __dir__(self) -> list[str]: ...
    @property
    def __all__(self) -> list[str]: ...

# Global instance
cmaps: Cmaps

# Custom colormaps
GnBu_RdPl: EnhancedLinearSegmentedColormap
GnBu_RdPl_r: EnhancedLinearSegmentedColormap
RdGyBu: EnhancedLinearSegmentedColormap
RdGyBu_r: EnhancedLinearSegmentedColormap
WtBuPl: EnhancedLinearSegmentedColormap
WtBuPl_r: EnhancedLinearSegmentedColormap
WtBuGn: EnhancedLinearSegmentedColormap
WtBuGn_r: EnhancedLinearSegmentedColormap
WtRdPl: EnhancedLinearSegmentedColormap
WtRdPl_r: EnhancedLinearSegmentedColormap
WtHeatRed: EnhancedLinearSegmentedColormap
WtHeatRed_r: EnhancedLinearSegmentedColormap

# Module-level colormap access (all colormaps available at module level)
def __getattr__(name: str) -> EnhancedLinearSegmentedColormap: ...
