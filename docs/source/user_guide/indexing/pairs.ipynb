{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["(indexing_pairs)=\n", "\n", "# Indexing/Filtering Pairs"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "import faninsar as fis"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["DatetimeIndex(['2020-01-01', '2020-01-13', '2020-01-25', '2020-02-06',\n", "               '2020-02-18', '2020-03-01', '2020-03-13', '2020-03-25',\n", "               '2020-04-06', '2020-04-18',\n", "               ...\n", "               '2024-09-12', '2024-09-24', '2024-10-06', '2024-10-18',\n", "               '2024-10-30', '2024-11-11', '2024-11-23', '2024-12-05',\n", "               '2024-12-17', '2024-12-29'],\n", "              dtype='datetime64[ns]', length=153, freq='12D')"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["dates = pd.date_range(start=\"2020-01-01\",end=\"2024-12-31\",freq=\"12D\")\n", "dates"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>         Pairs           \n", "       primary  secondary\n", "0   2020-01-01 2020-01-13\n", "1   2020-01-01 2020-01-25\n", "2   2020-01-01 2020-02-06\n", "3   2020-01-13 2020-01-25\n", "4   2020-01-13 2020-02-06\n", "..         ...        ...\n", "448 2024-11-23 2024-12-17\n", "449 2024-11-23 2024-12-29\n", "450 2024-12-05 2024-12-17\n", "451 2024-12-05 2024-12-29\n", "452 2024-12-17 2024-12-29\n", "\n", "[453 rows x 2 columns]</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>faninsar.Pairs</div><div class='xr-array-name'></div><ul class=\"xr-dim-list\"><li><span class=\"xr-has-index\">pairs</span>=453</li><li><span class=\"xr-has-index\">dates</span>=153</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-de0cdc27-838f-4a92-a02d-7518c572f144' class='xr-array-in' type='checkbox' checked><label for='section-de0cdc27-838f-4a92-a02d-7518c572f144' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>faninsar.Pairs<pairs=453,dates=153></span></div><div class='xr-array-data'><table><td><table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: center;\">\n", "      <th></th>\n", "      <th>primary</th>\n", "      <th>secondary</th>\n", "      <th>days</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2020-01-01</td>\n", "      <td>2020-01-13</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2020-01-01</td>\n", "      <td>2020-01-25</td>\n", "      <td>24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>451</th>\n", "      <td>2024-12-05</td>\n", "      <td>2024-12-29</td>\n", "      <td>24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>452</th>\n", "      <td>2024-12-17</td>\n", "      <td>2024-12-29</td>\n", "      <td>12</td>\n", "    </tr>\n", "  </tbody>\n", "</table></td><td><svg height=\"200\" width=\"200\">\n", "  <rect fill=\"#8B4903A0\" height=\"150.0\" width=\"80\" x=\"10\" y=\"25\"></rect>\n", "  <rect fill=\"#8B4903A0\" height=\"150.0\" width=\"80\" x=\"110\" y=\"25\"></rect>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"1\" x1=\"10\" x2=\"90\" y1=\"25.0\" y2=\"25.0\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"1\" x1=\"110\" x2=\"190\" y1=\"25.0\" y2=\"25.0\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"33.333333333333336\" y2=\"33.333333333333336\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"33.333333333333336\" y2=\"33.333333333333336\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"41.66666666666667\" y2=\"41.66666666666667\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"41.66666666666667\" y2=\"41.66666666666667\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"50.0\" y2=\"50.0\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"50.0\" y2=\"50.0\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"58.333333333333336\" y2=\"58.333333333333336\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"58.333333333333336\" y2=\"58.333333333333336\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"66.66666666666667\" y2=\"66.66666666666667\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"66.66666666666667\" y2=\"66.66666666666667\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"75.0\" y2=\"75.0\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"75.0\" y2=\"75.0\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"83.33333333333334\" y2=\"83.33333333333334\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"83.33333333333334\" y2=\"83.33333333333334\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"91.66666666666667\" y2=\"91.66666666666667\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"91.66666666666667\" y2=\"91.66666666666667\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"100.0\" y2=\"100.0\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"100.0\" y2=\"100.0\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"108.33333333333334\" y2=\"108.33333333333334\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"108.33333333333334\" y2=\"108.33333333333334\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"116.66666666666667\" y2=\"116.66666666666667\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"116.66666666666667\" y2=\"116.66666666666667\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"125.0\" y2=\"125.0\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"125.0\" y2=\"125.0\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"133.33333333333334\" y2=\"133.33333333333334\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"133.33333333333334\" y2=\"133.33333333333334\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"141.66666666666669\" y2=\"141.66666666666669\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"141.66666666666669\" y2=\"141.66666666666669\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"150.0\" y2=\"150.0\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"150.0\" y2=\"150.0\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"158.33333333333334\" y2=\"158.33333333333334\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"158.33333333333334\" y2=\"158.33333333333334\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"166.66666666666669\" y2=\"166.66666666666669\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"166.66666666666669\" y2=\"166.66666666666669\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"1\" x1=\"10\" x2=\"90\" y1=\"175.0\" y2=\"175.0\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"1\" x1=\"110\" x2=\"190\" y1=\"175.0\" y2=\"175.0\"></line>\n", "  <text style=\"font-size:0.85em; font-weight:bold; text-anchor:middle;\" x=\"50\" y=\"197\">primary</text>\n", "  <text style=\"font-size:0.85em; font-weight:bold; text-anchor:middle;\" x=\"150\" y=\"197\">secondary</text>\n", "  <text style=\"font-size:0.9em; font-weight:bold; text-anchor:middle;\" x=\"100\" y=\"15\">453 Pairs</text>\n", "</svg></td></table></div></div></li><li class='xr-section-item'><input id='section-d1731785-7884-46a8-84cc-f7e9a7f9ede3' class='xr-section-summary-in' type='checkbox'  ><label for='section-d1731785-7884-46a8-84cc-f7e9a7f9ede3' class='xr-section-summary' >Indexes: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class=\"xr-var-list\"><li class=\"xr-var-item\"><div class=\"xr-var-name\"><span class=\"xr-has-index\">primary</span></div><div class=\"xr-var-dtype\">Acquisition</div><div class=\"xr-var-preview xr-preview\">2020-01-01 2020-01-01 ... 2024-12-05 2024-12-17</div>\n", "    <input class=\"xr-var-attrs-in\" disabled=\"disabled\" id=\"attrs-f4709568-6147-4cfc-a4a9-55361d2e8097\" type=\"checkbox\"><label for=\"attrs-f4709568-6147-4cfc-a4a9-55361d2e8097\" title=\"Show/Hide attributes\"><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label>\n", "    <input class=\"xr-var-data-in\" id=\"data-106e2ef0-0940-49b7-bd75-85694f4fda95\" type=\"checkbox\"><label for=\"data-106e2ef0-0940-49b7-bd75-85694f4fda95\" title=\"Show/Hide data repr\"><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class=\"xr-var-attrs\"><div class=\"xr-var-attrs\">\n", "        <dl class=\"xr-attrs\"></dl>\n", "      </div></div><div class=\"xr-var-data\"><div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>Acquisition([&#x27;2020-01-01&#x27;, &#x27;2020-01-01&#x27;, &#x27;2020-01-01&#x27;, &#x27;2020-01-13&#x27;,\n", "             &#x27;2020-01-13&#x27;, &#x27;2020-01-13&#x27;, &#x27;2020-01-25&#x27;, &#x27;2020-01-25&#x27;,\n", "             &#x27;2020-01-25&#x27;, &#x27;2020-02-06&#x27;,\n", "             ...\n", "             &#x27;2024-10-30&#x27;, &#x27;2024-11-11&#x27;, &#x27;2024-11-11&#x27;, &#x27;2024-11-11&#x27;,\n", "             &#x27;2024-11-23&#x27;, &#x27;2024-11-23&#x27;, &#x27;2024-11-23&#x27;, &#x27;2024-12-05&#x27;,\n", "             &#x27;2024-12-05&#x27;, &#x27;2024-12-17&#x27;],\n", "            dtype=&#x27;datetime64[s]&#x27;, length=453, freq=None)</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>faninsar.Acquisition</div><div class='xr-array-name'></div><ul class='xr-dim-list'><li><span>dates</span>: 453</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-e3dfed3f-3f20-42ed-85db-6ba9fda6ec47' class='xr-array-in' type='checkbox' checked><label for='section-e3dfed3f-3f20-42ed-85db-6ba9fda6ec47' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>2020-01-01 2020-01-01 2020-01-01 ... 2024-12-05 2024-12-05 2024-12-17</span></div><div class='xr-array-data'><pre>array([&#x27;2020-01-01T00:00:00&#x27;, &#x27;2020-01-01T00:00:00&#x27;, &#x27;2020-01-01T00:00:00&#x27;,\n", "       ..., &#x27;2024-12-05T00:00:00&#x27;, &#x27;2024-12-05T00:00:00&#x27;,\n", "       &#x27;2024-12-17T00:00:00&#x27;], dtype=&#x27;datetime64[s]&#x27;)</pre></div></div></li><li class='xr-section-item'><input id='section-fe187c1f-d4a0-4929-aa5f-f4284a2b9603' class='xr-section-summary-in' type='checkbox'  checked><label for='section-fe187c1f-d4a0-4929-aa5f-f4284a2b9603' class='xr-section-summary' >Statistic: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><div style=\"width: 100%; margin: 0px 0px 0px 1em;\">\n", "  <table style=\"margin: 5px 2.5%; width: 95%;\">\n", "    <body>\n", "      <tr>\n", "        <th>start</th>\n", "        <td>2020-01-01</td>\n", "      </tr>\n", "      <tr>\n", "        <th>end</th>\n", "        <td>2024-12-17</td>\n", "      </tr>\n", "      <tr>\n", "        <th>unique</th>\n", "        <td>152</td>\n", "      </tr>\n", "      <tr>\n", "        <th>total</th>\n", "        <td>453</td>\n", "      </tr>\n", "    </body>\n", "  </table>\n", "</div></div></li></ul></div></div></div>\n", "  </li><li class=\"xr-var-item\"><div class=\"xr-var-name\"><span class=\"xr-has-index\">secondary</span></div><div class=\"xr-var-dtype\">Acquisition</div><div class=\"xr-var-preview xr-preview\">2020-01-13 2020-01-25 ... 2024-12-29 2024-12-29</div>\n", "    <input class=\"xr-var-attrs-in\" disabled=\"disabled\" id=\"attrs-7ed1b3bc-9111-45f1-b9f3-c16701b63f43\" type=\"checkbox\"><label for=\"attrs-7ed1b3bc-9111-45f1-b9f3-c16701b63f43\" title=\"Show/Hide attributes\"><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label>\n", "    <input class=\"xr-var-data-in\" id=\"data-cd6fbeda-5aa6-4117-8531-449a1b6ebd21\" type=\"checkbox\"><label for=\"data-cd6fbeda-5aa6-4117-8531-449a1b6ebd21\" title=\"Show/Hide data repr\"><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class=\"xr-var-attrs\"><div class=\"xr-var-attrs\">\n", "        <dl class=\"xr-attrs\"></dl>\n", "      </div></div><div class=\"xr-var-data\"><div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>Acquisition([&#x27;2020-01-13&#x27;, &#x27;2020-01-25&#x27;, &#x27;2020-02-06&#x27;, &#x27;2020-01-25&#x27;,\n", "             &#x27;2020-02-06&#x27;, &#x27;2020-02-18&#x27;, &#x27;2020-02-06&#x27;, &#x27;2020-02-18&#x27;,\n", "             &#x27;2020-03-01&#x27;, &#x27;2020-02-18&#x27;,\n", "             ...\n", "             &#x27;2024-12-05&#x27;, &#x27;2024-11-23&#x27;, &#x27;2024-12-05&#x27;, &#x27;2024-12-17&#x27;,\n", "             &#x27;2024-12-05&#x27;, &#x27;2024-12-17&#x27;, &#x27;2024-12-29&#x27;, &#x27;2024-12-17&#x27;,\n", "             &#x27;2024-12-29&#x27;, &#x27;2024-12-29&#x27;],\n", "            dtype=&#x27;datetime64[s]&#x27;, length=453, freq=None)</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>faninsar.Acquisition</div><div class='xr-array-name'></div><ul class='xr-dim-list'><li><span>dates</span>: 453</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-fb3cef0e-5dc7-42b0-8950-1daa86e41c22' class='xr-array-in' type='checkbox' checked><label for='section-fb3cef0e-5dc7-42b0-8950-1daa86e41c22' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>2020-01-13 2020-01-25 2020-02-06 ... 2024-12-17 2024-12-29 2024-12-29</span></div><div class='xr-array-data'><pre>array([&#x27;2020-01-13T00:00:00&#x27;, &#x27;2020-01-25T00:00:00&#x27;, &#x27;2020-02-06T00:00:00&#x27;,\n", "       ..., &#x27;2024-12-17T00:00:00&#x27;, &#x27;2024-12-29T00:00:00&#x27;,\n", "       &#x27;2024-12-29T00:00:00&#x27;], dtype=&#x27;datetime64[s]&#x27;)</pre></div></div></li><li class='xr-section-item'><input id='section-61d5ef28-d53e-4996-b38e-e08eea91256e' class='xr-section-summary-in' type='checkbox'  checked><label for='section-61d5ef28-d53e-4996-b38e-e08eea91256e' class='xr-section-summary' >Statistic: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><div style=\"width: 100%; margin: 0px 0px 0px 1em;\">\n", "  <table style=\"margin: 5px 2.5%; width: 95%;\">\n", "    <body>\n", "      <tr>\n", "        <th>start</th>\n", "        <td>2020-01-13</td>\n", "      </tr>\n", "      <tr>\n", "        <th>end</th>\n", "        <td>2024-12-29</td>\n", "      </tr>\n", "      <tr>\n", "        <th>unique</th>\n", "        <td>152</td>\n", "      </tr>\n", "      <tr>\n", "        <th>total</th>\n", "        <td>453</td>\n", "      </tr>\n", "    </body>\n", "  </table>\n", "</div></div></li></ul></div></div></div>\n", "  </li><li class=\"xr-var-item\"><div class=\"xr-var-name\"><span class=\"xr-has-index\">days</span></div><div class=\"xr-var-dtype\">DaySpan</div><div class=\"xr-var-preview xr-preview\">12 24 36 12 24 36 12 24 ... 36 12 24 36 12 24 12</div>\n", "    <input class=\"xr-var-attrs-in\" disabled=\"disabled\" id=\"attrs-987fd5a0-bd3a-4393-a4df-6b7028884ccf\" type=\"checkbox\"><label for=\"attrs-987fd5a0-bd3a-4393-a4df-6b7028884ccf\" title=\"Show/Hide attributes\"><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label>\n", "    <input class=\"xr-var-data-in\" id=\"data-ac8fc041-766b-4c35-94d4-fc62f01df08e\" type=\"checkbox\"><label for=\"data-ac8fc041-766b-4c35-94d4-fc62f01df08e\" title=\"Show/Hide data repr\"><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class=\"xr-var-attrs\"><div class=\"xr-var-attrs\">\n", "        <dl class=\"xr-attrs\"></dl>\n", "      </div></div><div class=\"xr-var-data\"><div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>DaySpan([12, 24, 36, 12, 24, 36, 12, 24, 36, 12,\n", "         ...\n", "         36, 12, 24, 36, 12, 24, 36, 12, 24, 12],\n", "        dtype=&#x27;int32&#x27;, length=453)</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>faninsar.DaySpan</div><div class='xr-array-name'></div><ul class='xr-dim-list'><li><span>days</span>: 453</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-1d7502ab-413f-48ac-9d3a-d57236c79dde' class='xr-array-in' type='checkbox' checked><label for='section-1d7502ab-413f-48ac-9d3a-d57236c79dde' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>12 24 36 12 24 36 12 24 36 12 24 ... 24 36 12 24 36 12 24 36 12 24 12</span></div><div class='xr-array-data'><pre>array([12, 24, 36, ..., 12, 24, 12], dtype=int32)</pre></div></div></li><li class='xr-section-item'><input id='section-e6bfd6e9-75e3-4bf3-8856-7fbf2be2fc8b' class='xr-section-summary-in' type='checkbox'  checked><label for='section-e6bfd6e9-75e3-4bf3-8856-7fbf2be2fc8b' class='xr-section-summary' >Statistic: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><div style=\"width: 100%; margin: 0px 0px 0px 1em;\">\n", "  <table style=\"margin: 5px 2.5%; width: 95%;\">\n", "    <body>\n", "      <tr>\n", "        <th>min</th>\n", "        <td>12</td>\n", "      </tr>\n", "      <tr>\n", "        <th>max</th>\n", "        <td>36</td>\n", "      </tr>\n", "      <tr>\n", "        <th>unique</th>\n", "        <td>3</td>\n", "      </tr>\n", "      <tr>\n", "        <th>total</th>\n", "        <td>453</td>\n", "      </tr>\n", "    </body>\n", "  </table>\n", "</div></div></li></ul></div></div></div>\n", "  </li><li class=\"xr-var-item\"><div class=\"xr-var-name\"><span class=\"xr-has-index\">dates</span></div><div class=\"xr-var-dtype\">Acquisition</div><div class=\"xr-var-preview xr-preview\">2020-01-01 2020-01-13 ... 2024-12-17 2024-12-29</div>\n", "    <input class=\"xr-var-attrs-in\" disabled=\"disabled\" id=\"attrs-6fe3ff06-8f42-455b-b023-f45a1b3e212f\" type=\"checkbox\"><label for=\"attrs-6fe3ff06-8f42-455b-b023-f45a1b3e212f\" title=\"Show/Hide attributes\"><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label>\n", "    <input class=\"xr-var-data-in\" id=\"data-1ff6bedd-d826-4df4-8516-e33099172c55\" type=\"checkbox\"><label for=\"data-1ff6bedd-d826-4df4-8516-e33099172c55\" title=\"Show/Hide data repr\"><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class=\"xr-var-attrs\"><div class=\"xr-var-attrs\">\n", "        <dl class=\"xr-attrs\"></dl>\n", "      </div></div><div class=\"xr-var-data\"><div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>Acquisition([&#x27;2020-01-01&#x27;, &#x27;2020-01-13&#x27;, &#x27;2020-01-25&#x27;, &#x27;2020-02-06&#x27;,\n", "             &#x27;2020-02-18&#x27;, &#x27;2020-03-01&#x27;, &#x27;2020-03-13&#x27;, &#x27;2020-03-25&#x27;,\n", "             &#x27;2020-04-06&#x27;, &#x27;2020-04-18&#x27;,\n", "             ...\n", "             &#x27;2024-09-12&#x27;, &#x27;2024-09-24&#x27;, &#x27;2024-10-06&#x27;, &#x27;2024-10-18&#x27;,\n", "             &#x27;2024-10-30&#x27;, &#x27;2024-11-11&#x27;, &#x27;2024-11-23&#x27;, &#x27;2024-12-05&#x27;,\n", "             &#x27;2024-12-17&#x27;, &#x27;2024-12-29&#x27;],\n", "            dtype=&#x27;datetime64[s]&#x27;, length=153, freq=None)</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>faninsar.Acquisition</div><div class='xr-array-name'></div><ul class='xr-dim-list'><li><span>dates</span>: 153</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-bfc1ea91-61fe-4233-a9be-5ebccfe54d2d' class='xr-array-in' type='checkbox' checked><label for='section-bfc1ea91-61fe-4233-a9be-5ebccfe54d2d' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>2020-01-01 2020-01-13 2020-01-25 ... 2024-12-05 2024-12-17 2024-12-29</span></div><div class='xr-array-data'><pre>array([&#x27;2020-01-01T00:00:00&#x27;, &#x27;2020-01-13T00:00:00&#x27;, &#x27;2020-01-25T00:00:00&#x27;,\n", "       &#x27;2020-02-06T00:00:00&#x27;, &#x27;2020-02-18T00:00:00&#x27;, &#x27;2020-03-01T00:00:00&#x27;,\n", "       &#x27;2020-03-13T00:00:00&#x27;, &#x27;2020-03-25T00:00:00&#x27;, &#x27;2020-04-06T00:00:00&#x27;,\n", "       &#x27;2020-04-18T00:00:00&#x27;, &#x27;2020-04-30T00:00:00&#x27;, &#x27;2020-05-12T00:00:00&#x27;,\n", "       &#x27;2020-05-24T00:00:00&#x27;, &#x27;2020-06-05T00:00:00&#x27;, &#x27;2020-06-17T00:00:00&#x27;,\n", "       &#x27;2020-06-29T00:00:00&#x27;, &#x27;2020-07-11T00:00:00&#x27;, &#x27;2020-07-23T00:00:00&#x27;,\n", "       &#x27;2020-08-04T00:00:00&#x27;, &#x27;2020-08-16T00:00:00&#x27;, &#x27;2020-08-28T00:00:00&#x27;,\n", "       &#x27;2020-09-09T00:00:00&#x27;, &#x27;2020-09-21T00:00:00&#x27;, &#x27;2020-10-03T00:00:00&#x27;,\n", "       &#x27;2020-10-15T00:00:00&#x27;, &#x27;2020-10-27T00:00:00&#x27;, &#x27;2020-11-08T00:00:00&#x27;,\n", "       &#x27;2020-11-20T00:00:00&#x27;, &#x27;2020-12-02T00:00:00&#x27;, &#x27;2020-12-14T00:00:00&#x27;,\n", "       &#x27;2020-12-26T00:00:00&#x27;, &#x27;2021-01-07T00:00:00&#x27;, &#x27;2021-01-19T00:00:00&#x27;,\n", "       &#x27;2021-01-31T00:00:00&#x27;, &#x27;2021-02-12T00:00:00&#x27;, &#x27;2021-02-24T00:00:00&#x27;,\n", "       &#x27;2021-03-08T00:00:00&#x27;, &#x27;2021-03-20T00:00:00&#x27;, &#x27;2021-04-01T00:00:00&#x27;,\n", "       &#x27;2021-04-13T00:00:00&#x27;, &#x27;2021-04-25T00:00:00&#x27;, &#x27;2021-05-07T00:00:00&#x27;,\n", "       &#x27;2021-05-19T00:00:00&#x27;, &#x27;2021-05-31T00:00:00&#x27;, &#x27;2021-06-12T00:00:00&#x27;,\n", "       &#x27;2021-06-24T00:00:00&#x27;, &#x27;2021-07-06T00:00:00&#x27;, &#x27;2021-07-18T00:00:00&#x27;,\n", "       &#x27;2021-07-30T00:00:00&#x27;, &#x27;2021-08-11T00:00:00&#x27;, &#x27;2021-08-23T00:00:00&#x27;,\n", "       &#x27;2021-09-04T00:00:00&#x27;, &#x27;2021-09-16T00:00:00&#x27;, &#x27;2021-09-28T00:00:00&#x27;,\n", "       &#x27;2021-10-10T00:00:00&#x27;, &#x27;2021-10-22T00:00:00&#x27;, &#x27;2021-11-03T00:00:00&#x27;,\n", "       &#x27;2021-11-15T00:00:00&#x27;, &#x27;2021-11-27T00:00:00&#x27;, &#x27;2021-12-09T00:00:00&#x27;,\n", "       &#x27;2021-12-21T00:00:00&#x27;, &#x27;2022-01-02T00:00:00&#x27;, &#x27;2022-01-14T00:00:00&#x27;,\n", "       &#x27;2022-01-26T00:00:00&#x27;, &#x27;2022-02-07T00:00:00&#x27;, &#x27;2022-02-19T00:00:00&#x27;,\n", "       &#x27;2022-03-03T00:00:00&#x27;, &#x27;2022-03-15T00:00:00&#x27;, &#x27;2022-03-27T00:00:00&#x27;,\n", "       &#x27;2022-04-08T00:00:00&#x27;, &#x27;2022-04-20T00:00:00&#x27;, &#x27;2022-05-02T00:00:00&#x27;,\n", "       &#x27;2022-05-14T00:00:00&#x27;, &#x27;2022-05-26T00:00:00&#x27;, &#x27;2022-06-07T00:00:00&#x27;,\n", "       &#x27;2022-06-19T00:00:00&#x27;, &#x27;2022-07-01T00:00:00&#x27;, &#x27;2022-07-13T00:00:00&#x27;,\n", "       &#x27;2022-07-25T00:00:00&#x27;, &#x27;2022-08-06T00:00:00&#x27;, &#x27;2022-08-18T00:00:00&#x27;,\n", "       &#x27;2022-08-30T00:00:00&#x27;, &#x27;2022-09-11T00:00:00&#x27;, &#x27;2022-09-23T00:00:00&#x27;,\n", "       &#x27;2022-10-05T00:00:00&#x27;, &#x27;2022-10-17T00:00:00&#x27;, &#x27;2022-10-29T00:00:00&#x27;,\n", "       &#x27;2022-11-10T00:00:00&#x27;, &#x27;2022-11-22T00:00:00&#x27;, &#x27;2022-12-04T00:00:00&#x27;,\n", "       &#x27;2022-12-16T00:00:00&#x27;, &#x27;2022-12-28T00:00:00&#x27;, &#x27;2023-01-09T00:00:00&#x27;,\n", "       &#x27;2023-01-21T00:00:00&#x27;, &#x27;2023-02-02T00:00:00&#x27;, &#x27;2023-02-14T00:00:00&#x27;,\n", "       &#x27;2023-02-26T00:00:00&#x27;, &#x27;2023-03-10T00:00:00&#x27;, &#x27;2023-03-22T00:00:00&#x27;,\n", "       &#x27;2023-04-03T00:00:00&#x27;, &#x27;2023-04-15T00:00:00&#x27;, &#x27;2023-04-27T00:00:00&#x27;,\n", "       &#x27;2023-05-09T00:00:00&#x27;, &#x27;2023-05-21T00:00:00&#x27;, &#x27;2023-06-02T00:00:00&#x27;,\n", "       &#x27;2023-06-14T00:00:00&#x27;, &#x27;2023-06-26T00:00:00&#x27;, &#x27;2023-07-08T00:00:00&#x27;,\n", "       &#x27;2023-07-20T00:00:00&#x27;, &#x27;2023-08-01T00:00:00&#x27;, &#x27;2023-08-13T00:00:00&#x27;,\n", "       &#x27;2023-08-25T00:00:00&#x27;, &#x27;2023-09-06T00:00:00&#x27;, &#x27;2023-09-18T00:00:00&#x27;,\n", "       &#x27;2023-09-30T00:00:00&#x27;, &#x27;2023-10-12T00:00:00&#x27;, &#x27;2023-10-24T00:00:00&#x27;,\n", "       &#x27;2023-11-05T00:00:00&#x27;, &#x27;2023-11-17T00:00:00&#x27;, &#x27;2023-11-29T00:00:00&#x27;,\n", "       &#x27;2023-12-11T00:00:00&#x27;, &#x27;2023-12-23T00:00:00&#x27;, &#x27;2024-01-04T00:00:00&#x27;,\n", "       &#x27;2024-01-16T00:00:00&#x27;, &#x27;2024-01-28T00:00:00&#x27;, &#x27;2024-02-09T00:00:00&#x27;,\n", "       &#x27;2024-02-21T00:00:00&#x27;, &#x27;2024-03-04T00:00:00&#x27;, &#x27;2024-03-16T00:00:00&#x27;,\n", "       &#x27;2024-03-28T00:00:00&#x27;, &#x27;2024-04-09T00:00:00&#x27;, &#x27;2024-04-21T00:00:00&#x27;,\n", "       &#x27;2024-05-03T00:00:00&#x27;, &#x27;2024-05-15T00:00:00&#x27;, &#x27;2024-05-27T00:00:00&#x27;,\n", "       &#x27;2024-06-08T00:00:00&#x27;, &#x27;2024-06-20T00:00:00&#x27;, &#x27;2024-07-02T00:00:00&#x27;,\n", "       &#x27;2024-07-14T00:00:00&#x27;, &#x27;2024-07-26T00:00:00&#x27;, &#x27;2024-08-07T00:00:00&#x27;,\n", "       &#x27;2024-08-19T00:00:00&#x27;, &#x27;2024-08-31T00:00:00&#x27;, &#x27;2024-09-12T00:00:00&#x27;,\n", "       &#x27;2024-09-24T00:00:00&#x27;, &#x27;2024-10-06T00:00:00&#x27;, &#x27;2024-10-18T00:00:00&#x27;,\n", "       &#x27;2024-10-30T00:00:00&#x27;, &#x27;2024-11-11T00:00:00&#x27;, &#x27;2024-11-23T00:00:00&#x27;,\n", "       &#x27;2024-12-05T00:00:00&#x27;, &#x27;2024-12-17T00:00:00&#x27;, &#x27;2024-12-29T00:00:00&#x27;],\n", "      dtype=&#x27;datetime64[s]&#x27;)</pre></div></div></li><li class='xr-section-item'><input id='section-834ece31-73c3-4c9a-80e9-f9a7aee366e9' class='xr-section-summary-in' type='checkbox'  checked><label for='section-834ece31-73c3-4c9a-80e9-f9a7aee366e9' class='xr-section-summary' >Statistic: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><div style=\"width: 100%; margin: 0px 0px 0px 1em;\">\n", "  <table style=\"margin: 5px 2.5%; width: 95%;\">\n", "    <body>\n", "      <tr>\n", "        <th>start</th>\n", "        <td>2020-01-01</td>\n", "      </tr>\n", "      <tr>\n", "        <th>end</th>\n", "        <td>2024-12-29</td>\n", "      </tr>\n", "      <tr>\n", "        <th>unique</th>\n", "        <td>153</td>\n", "      </tr>\n", "      <tr>\n", "        <th>total</th>\n", "        <td>153</td>\n", "      </tr>\n", "    </body>\n", "  </table>\n", "</div></div></li></ul></div></div></div>\n", "  </li></ul></div></li></ul></div></div>"], "text/plain": ["         Pairs           \n", "       primary  secondary\n", "0   2020-01-01 2020-01-13\n", "1   2020-01-01 2020-01-25\n", "2   2020-01-01 2020-02-06\n", "3   2020-01-13 2020-01-25\n", "4   2020-01-13 2020-02-06\n", "..         ...        ...\n", "448 2024-11-23 2024-12-17\n", "449 2024-11-23 2024-12-29\n", "450 2024-12-05 2024-12-17\n", "451 2024-12-05 2024-12-29\n", "452 2024-12-17 2024-12-29\n", "\n", "[453 rows x 2 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["pairs_factory = fis.PairsFactory(dates)\n", "pairs = pairs_factory.from_interval(max_interval=3)\n", "pairs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Filter Pairs date range\n", "\n", "If you want to filter pairs using the start and end dates, you can directly slice the pairs just like you would slice a pandas datetime index.\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>         Pairs           \n", "       primary  secondary\n", "0   2022-01-02 2022-01-14\n", "1   2022-01-02 2022-01-26\n", "2   2022-01-02 2022-02-07\n", "3   2022-01-14 2022-01-26\n", "4   2022-01-14 2022-02-07\n", "..         ...        ...\n", "190 2024-01-28 2024-02-21\n", "191 2024-01-28 2024-03-04\n", "192 2024-02-09 2024-02-21\n", "193 2024-02-09 2024-03-04\n", "194 2024-02-21 2024-03-04\n", "\n", "[195 rows x 2 columns]</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>faninsar.Pairs</div><div class='xr-array-name'></div><ul class=\"xr-dim-list\"><li><span class=\"xr-has-index\">pairs</span>=195</li><li><span class=\"xr-has-index\">dates</span>=67</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-636f674a-3426-44a5-beca-85a0acbd7e1f' class='xr-array-in' type='checkbox' checked><label for='section-636f674a-3426-44a5-beca-85a0acbd7e1f' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>faninsar.Pairs<pairs=195,dates=67></span></div><div class='xr-array-data'><table><td><table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: center;\">\n", "      <th></th>\n", "      <th>primary</th>\n", "      <th>secondary</th>\n", "      <th>days</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2022-01-02</td>\n", "      <td>2022-01-14</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2022-01-02</td>\n", "      <td>2022-01-26</td>\n", "      <td>24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>193</th>\n", "      <td>2024-02-09</td>\n", "      <td>2024-03-04</td>\n", "      <td>24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>194</th>\n", "      <td>2024-02-21</td>\n", "      <td>2024-03-04</td>\n", "      <td>12</td>\n", "    </tr>\n", "  </tbody>\n", "</table></td><td><svg height=\"200\" width=\"200\">\n", "  <rect fill=\"#8B4903A0\" height=\"150.0\" width=\"80\" x=\"10\" y=\"25\"></rect>\n", "  <rect fill=\"#8B4903A0\" height=\"150.0\" width=\"80\" x=\"110\" y=\"25\"></rect>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"1\" x1=\"10\" x2=\"90\" y1=\"25.0\" y2=\"25.0\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"1\" x1=\"110\" x2=\"190\" y1=\"25.0\" y2=\"25.0\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"36.53846153846154\" y2=\"36.53846153846154\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"36.53846153846154\" y2=\"36.53846153846154\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"48.07692307692308\" y2=\"48.07692307692308\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"48.07692307692308\" y2=\"48.07692307692308\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"59.61538461538461\" y2=\"59.61538461538461\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"59.61538461538461\" y2=\"59.61538461538461\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"71.15384615384616\" y2=\"71.15384615384616\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"71.15384615384616\" y2=\"71.15384615384616\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"82.6923076923077\" y2=\"82.6923076923077\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"82.6923076923077\" y2=\"82.6923076923077\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"94.23076923076923\" y2=\"94.23076923076923\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"94.23076923076923\" y2=\"94.23076923076923\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"105.76923076923077\" y2=\"105.76923076923077\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"105.76923076923077\" y2=\"105.76923076923077\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"117.3076923076923\" y2=\"117.3076923076923\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"117.3076923076923\" y2=\"117.3076923076923\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"128.84615384615384\" y2=\"128.84615384615384\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"128.84615384615384\" y2=\"128.84615384615384\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"140.3846153846154\" y2=\"140.3846153846154\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"140.3846153846154\" y2=\"140.3846153846154\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"151.9230769230769\" y2=\"151.9230769230769\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"151.9230769230769\" y2=\"151.9230769230769\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"163.46153846153845\" y2=\"163.46153846153845\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"163.46153846153845\" y2=\"163.46153846153845\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"1\" x1=\"10\" x2=\"90\" y1=\"175.0\" y2=\"175.0\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"1\" x1=\"110\" x2=\"190\" y1=\"175.0\" y2=\"175.0\"></line>\n", "  <text style=\"font-size:0.85em; font-weight:bold; text-anchor:middle;\" x=\"50\" y=\"197\">primary</text>\n", "  <text style=\"font-size:0.85em; font-weight:bold; text-anchor:middle;\" x=\"150\" y=\"197\">secondary</text>\n", "  <text style=\"font-size:0.9em; font-weight:bold; text-anchor:middle;\" x=\"100\" y=\"15\">195 Pairs</text>\n", "</svg></td></table></div></div></li><li class='xr-section-item'><input id='section-464d445d-9865-46b9-a4e8-6d4ff027fe41' class='xr-section-summary-in' type='checkbox'  ><label for='section-464d445d-9865-46b9-a4e8-6d4ff027fe41' class='xr-section-summary' >Indexes: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class=\"xr-var-list\"><li class=\"xr-var-item\"><div class=\"xr-var-name\"><span class=\"xr-has-index\">primary</span></div><div class=\"xr-var-dtype\">Acquisition</div><div class=\"xr-var-preview xr-preview\">2022-01-02 2022-01-02 ... 2024-02-09 2024-02-21</div>\n", "    <input class=\"xr-var-attrs-in\" disabled=\"disabled\" id=\"attrs-42e5a271-9d5b-4464-ab3e-4e3f31d1796e\" type=\"checkbox\"><label for=\"attrs-42e5a271-9d5b-4464-ab3e-4e3f31d1796e\" title=\"Show/Hide attributes\"><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label>\n", "    <input class=\"xr-var-data-in\" id=\"data-fb287a6d-ed15-46ff-8afa-31cc6aa8b55e\" type=\"checkbox\"><label for=\"data-fb287a6d-ed15-46ff-8afa-31cc6aa8b55e\" title=\"Show/Hide data repr\"><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class=\"xr-var-attrs\"><div class=\"xr-var-attrs\">\n", "        <dl class=\"xr-attrs\"></dl>\n", "      </div></div><div class=\"xr-var-data\"><div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>Acquisition([&#x27;2022-01-02&#x27;, &#x27;2022-01-02&#x27;, &#x27;2022-01-02&#x27;, &#x27;2022-01-14&#x27;,\n", "             &#x27;2022-01-14&#x27;, &#x27;2022-01-14&#x27;, &#x27;2022-01-26&#x27;, &#x27;2022-01-26&#x27;,\n", "             &#x27;2022-01-26&#x27;, &#x27;2022-02-07&#x27;,\n", "             ...\n", "             &#x27;2024-01-04&#x27;, &#x27;2024-01-16&#x27;, &#x27;2024-01-16&#x27;, &#x27;2024-01-16&#x27;,\n", "             &#x27;2024-01-28&#x27;, &#x27;2024-01-28&#x27;, &#x27;2024-01-28&#x27;, &#x27;2024-02-09&#x27;,\n", "             &#x27;2024-02-09&#x27;, &#x27;2024-02-21&#x27;],\n", "            dtype=&#x27;datetime64[s]&#x27;, length=195, freq=None)</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>faninsar.Acquisition</div><div class='xr-array-name'></div><ul class='xr-dim-list'><li><span>dates</span>: 195</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-e3279dcb-d107-422c-b05b-20dd3cfc391d' class='xr-array-in' type='checkbox' checked><label for='section-e3279dcb-d107-422c-b05b-20dd3cfc391d' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>2022-01-02 2022-01-02 2022-01-02 ... 2024-02-09 2024-02-09 2024-02-21</span></div><div class='xr-array-data'><pre>array([&#x27;2022-01-02T00:00:00&#x27;, &#x27;2022-01-02T00:00:00&#x27;, &#x27;2022-01-02T00:00:00&#x27;,\n", "       &#x27;2022-01-14T00:00:00&#x27;, &#x27;2022-01-14T00:00:00&#x27;, &#x27;2022-01-14T00:00:00&#x27;,\n", "       &#x27;2022-01-26T00:00:00&#x27;, &#x27;2022-01-26T00:00:00&#x27;, &#x27;2022-01-26T00:00:00&#x27;,\n", "       &#x27;2022-02-07T00:00:00&#x27;, &#x27;2022-02-07T00:00:00&#x27;, &#x27;2022-02-07T00:00:00&#x27;,\n", "       &#x27;2022-02-19T00:00:00&#x27;, &#x27;2022-02-19T00:00:00&#x27;, &#x27;2022-02-19T00:00:00&#x27;,\n", "       &#x27;2022-03-03T00:00:00&#x27;, &#x27;2022-03-03T00:00:00&#x27;, &#x27;2022-03-03T00:00:00&#x27;,\n", "       &#x27;2022-03-15T00:00:00&#x27;, &#x27;2022-03-15T00:00:00&#x27;, &#x27;2022-03-15T00:00:00&#x27;,\n", "       &#x27;2022-03-27T00:00:00&#x27;, &#x27;2022-03-27T00:00:00&#x27;, &#x27;2022-03-27T00:00:00&#x27;,\n", "       &#x27;2022-04-08T00:00:00&#x27;, &#x27;2022-04-08T00:00:00&#x27;, &#x27;2022-04-08T00:00:00&#x27;,\n", "       &#x27;2022-04-20T00:00:00&#x27;, &#x27;2022-04-20T00:00:00&#x27;, &#x27;2022-04-20T00:00:00&#x27;,\n", "       &#x27;2022-05-02T00:00:00&#x27;, &#x27;2022-05-02T00:00:00&#x27;, &#x27;2022-05-02T00:00:00&#x27;,\n", "       &#x27;2022-05-14T00:00:00&#x27;, &#x27;2022-05-14T00:00:00&#x27;, &#x27;2022-05-14T00:00:00&#x27;,\n", "       &#x27;2022-05-26T00:00:00&#x27;, &#x27;2022-05-26T00:00:00&#x27;, &#x27;2022-05-26T00:00:00&#x27;,\n", "       &#x27;2022-06-07T00:00:00&#x27;, &#x27;2022-06-07T00:00:00&#x27;, &#x27;2022-06-07T00:00:00&#x27;,\n", "       &#x27;2022-06-19T00:00:00&#x27;, &#x27;2022-06-19T00:00:00&#x27;, &#x27;2022-06-19T00:00:00&#x27;,\n", "       &#x27;2022-07-01T00:00:00&#x27;, &#x27;2022-07-01T00:00:00&#x27;, &#x27;2022-07-01T00:00:00&#x27;,\n", "       &#x27;2022-07-13T00:00:00&#x27;, &#x27;2022-07-13T00:00:00&#x27;, &#x27;2022-07-13T00:00:00&#x27;,\n", "       &#x27;2022-07-25T00:00:00&#x27;, &#x27;2022-07-25T00:00:00&#x27;, &#x27;2022-07-25T00:00:00&#x27;,\n", "       &#x27;2022-08-06T00:00:00&#x27;, &#x27;2022-08-06T00:00:00&#x27;, &#x27;2022-08-06T00:00:00&#x27;,\n", "       &#x27;2022-08-18T00:00:00&#x27;, &#x27;2022-08-18T00:00:00&#x27;, &#x27;2022-08-18T00:00:00&#x27;,\n", "       &#x27;2022-08-30T00:00:00&#x27;, &#x27;2022-08-30T00:00:00&#x27;, &#x27;2022-08-30T00:00:00&#x27;,\n", "       &#x27;2022-09-11T00:00:00&#x27;, &#x27;2022-09-11T00:00:00&#x27;, &#x27;2022-09-11T00:00:00&#x27;,\n", "       &#x27;2022-09-23T00:00:00&#x27;, &#x27;2022-09-23T00:00:00&#x27;, &#x27;2022-09-23T00:00:00&#x27;,\n", "       &#x27;2022-10-05T00:00:00&#x27;, &#x27;2022-10-05T00:00:00&#x27;, &#x27;2022-10-05T00:00:00&#x27;,\n", "       &#x27;2022-10-17T00:00:00&#x27;, &#x27;2022-10-17T00:00:00&#x27;, &#x27;2022-10-17T00:00:00&#x27;,\n", "       &#x27;2022-10-29T00:00:00&#x27;, &#x27;2022-10-29T00:00:00&#x27;, &#x27;2022-10-29T00:00:00&#x27;,\n", "       &#x27;2022-11-10T00:00:00&#x27;, &#x27;2022-11-10T00:00:00&#x27;, &#x27;2022-11-10T00:00:00&#x27;,\n", "       &#x27;2022-11-22T00:00:00&#x27;, &#x27;2022-11-22T00:00:00&#x27;, &#x27;2022-11-22T00:00:00&#x27;,\n", "       &#x27;2022-12-04T00:00:00&#x27;, &#x27;2022-12-04T00:00:00&#x27;, &#x27;2022-12-04T00:00:00&#x27;,\n", "       &#x27;2022-12-16T00:00:00&#x27;, &#x27;2022-12-16T00:00:00&#x27;, &#x27;2022-12-16T00:00:00&#x27;,\n", "       &#x27;2022-12-28T00:00:00&#x27;, &#x27;2022-12-28T00:00:00&#x27;, &#x27;2022-12-28T00:00:00&#x27;,\n", "       &#x27;2023-01-09T00:00:00&#x27;, &#x27;2023-01-09T00:00:00&#x27;, &#x27;2023-01-09T00:00:00&#x27;,\n", "       &#x27;2023-01-21T00:00:00&#x27;, &#x27;2023-01-21T00:00:00&#x27;, &#x27;2023-01-21T00:00:00&#x27;,\n", "       &#x27;2023-02-02T00:00:00&#x27;, &#x27;2023-02-02T00:00:00&#x27;, &#x27;2023-02-02T00:00:00&#x27;,\n", "       &#x27;2023-02-14T00:00:00&#x27;, &#x27;2023-02-14T00:00:00&#x27;, &#x27;2023-02-14T00:00:00&#x27;,\n", "       &#x27;2023-02-26T00:00:00&#x27;, &#x27;2023-02-26T00:00:00&#x27;, &#x27;2023-02-26T00:00:00&#x27;,\n", "       &#x27;2023-03-10T00:00:00&#x27;, &#x27;2023-03-10T00:00:00&#x27;, &#x27;2023-03-10T00:00:00&#x27;,\n", "       &#x27;2023-03-22T00:00:00&#x27;, &#x27;2023-03-22T00:00:00&#x27;, &#x27;2023-03-22T00:00:00&#x27;,\n", "       &#x27;2023-04-03T00:00:00&#x27;, &#x27;2023-04-03T00:00:00&#x27;, &#x27;2023-04-03T00:00:00&#x27;,\n", "       &#x27;2023-04-15T00:00:00&#x27;, &#x27;2023-04-15T00:00:00&#x27;, &#x27;2023-04-15T00:00:00&#x27;,\n", "       &#x27;2023-04-27T00:00:00&#x27;, &#x27;2023-04-27T00:00:00&#x27;, &#x27;2023-04-27T00:00:00&#x27;,\n", "       &#x27;2023-05-09T00:00:00&#x27;, &#x27;2023-05-09T00:00:00&#x27;, &#x27;2023-05-09T00:00:00&#x27;,\n", "       &#x27;2023-05-21T00:00:00&#x27;, &#x27;2023-05-21T00:00:00&#x27;, &#x27;2023-05-21T00:00:00&#x27;,\n", "       &#x27;2023-06-02T00:00:00&#x27;, &#x27;2023-06-02T00:00:00&#x27;, &#x27;2023-06-02T00:00:00&#x27;,\n", "       &#x27;2023-06-14T00:00:00&#x27;, &#x27;2023-06-14T00:00:00&#x27;, &#x27;2023-06-14T00:00:00&#x27;,\n", "       &#x27;2023-06-26T00:00:00&#x27;, &#x27;2023-06-26T00:00:00&#x27;, &#x27;2023-06-26T00:00:00&#x27;,\n", "       &#x27;2023-07-08T00:00:00&#x27;, &#x27;2023-07-08T00:00:00&#x27;, &#x27;2023-07-08T00:00:00&#x27;,\n", "       &#x27;2023-07-20T00:00:00&#x27;, &#x27;2023-07-20T00:00:00&#x27;, &#x27;2023-07-20T00:00:00&#x27;,\n", "       &#x27;2023-08-01T00:00:00&#x27;, &#x27;2023-08-01T00:00:00&#x27;, &#x27;2023-08-01T00:00:00&#x27;,\n", "       &#x27;2023-08-13T00:00:00&#x27;, &#x27;2023-08-13T00:00:00&#x27;, &#x27;2023-08-13T00:00:00&#x27;,\n", "       &#x27;2023-08-25T00:00:00&#x27;, &#x27;2023-08-25T00:00:00&#x27;, &#x27;2023-08-25T00:00:00&#x27;,\n", "       &#x27;2023-09-06T00:00:00&#x27;, &#x27;2023-09-06T00:00:00&#x27;, &#x27;2023-09-06T00:00:00&#x27;,\n", "       &#x27;2023-09-18T00:00:00&#x27;, &#x27;2023-09-18T00:00:00&#x27;, &#x27;2023-09-18T00:00:00&#x27;,\n", "       &#x27;2023-09-30T00:00:00&#x27;, &#x27;2023-09-30T00:00:00&#x27;, &#x27;2023-09-30T00:00:00&#x27;,\n", "       &#x27;2023-10-12T00:00:00&#x27;, &#x27;2023-10-12T00:00:00&#x27;, &#x27;2023-10-12T00:00:00&#x27;,\n", "       &#x27;2023-10-24T00:00:00&#x27;, &#x27;2023-10-24T00:00:00&#x27;, &#x27;2023-10-24T00:00:00&#x27;,\n", "       &#x27;2023-11-05T00:00:00&#x27;, &#x27;2023-11-05T00:00:00&#x27;, &#x27;2023-11-05T00:00:00&#x27;,\n", "       &#x27;2023-11-17T00:00:00&#x27;, &#x27;2023-11-17T00:00:00&#x27;, &#x27;2023-11-17T00:00:00&#x27;,\n", "       &#x27;2023-11-29T00:00:00&#x27;, &#x27;2023-11-29T00:00:00&#x27;, &#x27;2023-11-29T00:00:00&#x27;,\n", "       &#x27;2023-12-11T00:00:00&#x27;, &#x27;2023-12-11T00:00:00&#x27;, &#x27;2023-12-11T00:00:00&#x27;,\n", "       &#x27;2023-12-23T00:00:00&#x27;, &#x27;2023-12-23T00:00:00&#x27;, &#x27;2023-12-23T00:00:00&#x27;,\n", "       &#x27;2024-01-04T00:00:00&#x27;, &#x27;2024-01-04T00:00:00&#x27;, &#x27;2024-01-04T00:00:00&#x27;,\n", "       &#x27;2024-01-16T00:00:00&#x27;, &#x27;2024-01-16T00:00:00&#x27;, &#x27;2024-01-16T00:00:00&#x27;,\n", "       &#x27;2024-01-28T00:00:00&#x27;, &#x27;2024-01-28T00:00:00&#x27;, &#x27;2024-01-28T00:00:00&#x27;,\n", "       &#x27;2024-02-09T00:00:00&#x27;, &#x27;2024-02-09T00:00:00&#x27;, &#x27;2024-02-21T00:00:00&#x27;],\n", "      dtype=&#x27;datetime64[s]&#x27;)</pre></div></div></li><li class='xr-section-item'><input id='section-54a397a1-3d27-4ac8-a06f-acada8a6cf05' class='xr-section-summary-in' type='checkbox'  checked><label for='section-54a397a1-3d27-4ac8-a06f-acada8a6cf05' class='xr-section-summary' >Statistic: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><div style=\"width: 100%; margin: 0px 0px 0px 1em;\">\n", "  <table style=\"margin: 5px 2.5%; width: 95%;\">\n", "    <body>\n", "      <tr>\n", "        <th>start</th>\n", "        <td>2022-01-02</td>\n", "      </tr>\n", "      <tr>\n", "        <th>end</th>\n", "        <td>2024-02-21</td>\n", "      </tr>\n", "      <tr>\n", "        <th>unique</th>\n", "        <td>66</td>\n", "      </tr>\n", "      <tr>\n", "        <th>total</th>\n", "        <td>195</td>\n", "      </tr>\n", "    </body>\n", "  </table>\n", "</div></div></li></ul></div></div></div>\n", "  </li><li class=\"xr-var-item\"><div class=\"xr-var-name\"><span class=\"xr-has-index\">secondary</span></div><div class=\"xr-var-dtype\">Acquisition</div><div class=\"xr-var-preview xr-preview\">2022-01-14 2022-01-26 ... 2024-03-04 2024-03-04</div>\n", "    <input class=\"xr-var-attrs-in\" disabled=\"disabled\" id=\"attrs-0e587c70-95b7-40d5-834b-1471384cc37c\" type=\"checkbox\"><label for=\"attrs-0e587c70-95b7-40d5-834b-1471384cc37c\" title=\"Show/Hide attributes\"><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label>\n", "    <input class=\"xr-var-data-in\" id=\"data-2508cd90-3084-4e83-a738-e340d0c381e0\" type=\"checkbox\"><label for=\"data-2508cd90-3084-4e83-a738-e340d0c381e0\" title=\"Show/Hide data repr\"><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class=\"xr-var-attrs\"><div class=\"xr-var-attrs\">\n", "        <dl class=\"xr-attrs\"></dl>\n", "      </div></div><div class=\"xr-var-data\"><div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>Acquisition([&#x27;2022-01-14&#x27;, &#x27;2022-01-26&#x27;, &#x27;2022-02-07&#x27;, &#x27;2022-01-26&#x27;,\n", "             &#x27;2022-02-07&#x27;, &#x27;2022-02-19&#x27;, &#x27;2022-02-07&#x27;, &#x27;2022-02-19&#x27;,\n", "             &#x27;2022-03-03&#x27;, &#x27;2022-02-19&#x27;,\n", "             ...\n", "             &#x27;2024-02-09&#x27;, &#x27;2024-01-28&#x27;, &#x27;2024-02-09&#x27;, &#x27;2024-02-21&#x27;,\n", "             &#x27;2024-02-09&#x27;, &#x27;2024-02-21&#x27;, &#x27;2024-03-04&#x27;, &#x27;2024-02-21&#x27;,\n", "             &#x27;2024-03-04&#x27;, &#x27;2024-03-04&#x27;],\n", "            dtype=&#x27;datetime64[s]&#x27;, length=195, freq=None)</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>faninsar.Acquisition</div><div class='xr-array-name'></div><ul class='xr-dim-list'><li><span>dates</span>: 195</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-393f0985-0eb7-4b14-b2e7-c0caf39677f1' class='xr-array-in' type='checkbox' checked><label for='section-393f0985-0eb7-4b14-b2e7-c0caf39677f1' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>2022-01-14 2022-01-26 2022-02-07 ... 2024-02-21 2024-03-04 2024-03-04</span></div><div class='xr-array-data'><pre>array([&#x27;2022-01-14T00:00:00&#x27;, &#x27;2022-01-26T00:00:00&#x27;, &#x27;2022-02-07T00:00:00&#x27;,\n", "       &#x27;2022-01-26T00:00:00&#x27;, &#x27;2022-02-07T00:00:00&#x27;, &#x27;2022-02-19T00:00:00&#x27;,\n", "       &#x27;2022-02-07T00:00:00&#x27;, &#x27;2022-02-19T00:00:00&#x27;, &#x27;2022-03-03T00:00:00&#x27;,\n", "       &#x27;2022-02-19T00:00:00&#x27;, &#x27;2022-03-03T00:00:00&#x27;, &#x27;2022-03-15T00:00:00&#x27;,\n", "       &#x27;2022-03-03T00:00:00&#x27;, &#x27;2022-03-15T00:00:00&#x27;, &#x27;2022-03-27T00:00:00&#x27;,\n", "       &#x27;2022-03-15T00:00:00&#x27;, &#x27;2022-03-27T00:00:00&#x27;, &#x27;2022-04-08T00:00:00&#x27;,\n", "       &#x27;2022-03-27T00:00:00&#x27;, &#x27;2022-04-08T00:00:00&#x27;, &#x27;2022-04-20T00:00:00&#x27;,\n", "       &#x27;2022-04-08T00:00:00&#x27;, &#x27;2022-04-20T00:00:00&#x27;, &#x27;2022-05-02T00:00:00&#x27;,\n", "       &#x27;2022-04-20T00:00:00&#x27;, &#x27;2022-05-02T00:00:00&#x27;, &#x27;2022-05-14T00:00:00&#x27;,\n", "       &#x27;2022-05-02T00:00:00&#x27;, &#x27;2022-05-14T00:00:00&#x27;, &#x27;2022-05-26T00:00:00&#x27;,\n", "       &#x27;2022-05-14T00:00:00&#x27;, &#x27;2022-05-26T00:00:00&#x27;, &#x27;2022-06-07T00:00:00&#x27;,\n", "       &#x27;2022-05-26T00:00:00&#x27;, &#x27;2022-06-07T00:00:00&#x27;, &#x27;2022-06-19T00:00:00&#x27;,\n", "       &#x27;2022-06-07T00:00:00&#x27;, &#x27;2022-06-19T00:00:00&#x27;, &#x27;2022-07-01T00:00:00&#x27;,\n", "       &#x27;2022-06-19T00:00:00&#x27;, &#x27;2022-07-01T00:00:00&#x27;, &#x27;2022-07-13T00:00:00&#x27;,\n", "       &#x27;2022-07-01T00:00:00&#x27;, &#x27;2022-07-13T00:00:00&#x27;, &#x27;2022-07-25T00:00:00&#x27;,\n", "       &#x27;2022-07-13T00:00:00&#x27;, &#x27;2022-07-25T00:00:00&#x27;, &#x27;2022-08-06T00:00:00&#x27;,\n", "       &#x27;2022-07-25T00:00:00&#x27;, &#x27;2022-08-06T00:00:00&#x27;, &#x27;2022-08-18T00:00:00&#x27;,\n", "       &#x27;2022-08-06T00:00:00&#x27;, &#x27;2022-08-18T00:00:00&#x27;, &#x27;2022-08-30T00:00:00&#x27;,\n", "       &#x27;2022-08-18T00:00:00&#x27;, &#x27;2022-08-30T00:00:00&#x27;, &#x27;2022-09-11T00:00:00&#x27;,\n", "       &#x27;2022-08-30T00:00:00&#x27;, &#x27;2022-09-11T00:00:00&#x27;, &#x27;2022-09-23T00:00:00&#x27;,\n", "       &#x27;2022-09-11T00:00:00&#x27;, &#x27;2022-09-23T00:00:00&#x27;, &#x27;2022-10-05T00:00:00&#x27;,\n", "       &#x27;2022-09-23T00:00:00&#x27;, &#x27;2022-10-05T00:00:00&#x27;, &#x27;2022-10-17T00:00:00&#x27;,\n", "       &#x27;2022-10-05T00:00:00&#x27;, &#x27;2022-10-17T00:00:00&#x27;, &#x27;2022-10-29T00:00:00&#x27;,\n", "       &#x27;2022-10-17T00:00:00&#x27;, &#x27;2022-10-29T00:00:00&#x27;, &#x27;2022-11-10T00:00:00&#x27;,\n", "       &#x27;2022-10-29T00:00:00&#x27;, &#x27;2022-11-10T00:00:00&#x27;, &#x27;2022-11-22T00:00:00&#x27;,\n", "       &#x27;2022-11-10T00:00:00&#x27;, &#x27;2022-11-22T00:00:00&#x27;, &#x27;2022-12-04T00:00:00&#x27;,\n", "       &#x27;2022-11-22T00:00:00&#x27;, &#x27;2022-12-04T00:00:00&#x27;, &#x27;2022-12-16T00:00:00&#x27;,\n", "       &#x27;2022-12-04T00:00:00&#x27;, &#x27;2022-12-16T00:00:00&#x27;, &#x27;2022-12-28T00:00:00&#x27;,\n", "       &#x27;2022-12-16T00:00:00&#x27;, &#x27;2022-12-28T00:00:00&#x27;, &#x27;2023-01-09T00:00:00&#x27;,\n", "       &#x27;2022-12-28T00:00:00&#x27;, &#x27;2023-01-09T00:00:00&#x27;, &#x27;2023-01-21T00:00:00&#x27;,\n", "       &#x27;2023-01-09T00:00:00&#x27;, &#x27;2023-01-21T00:00:00&#x27;, &#x27;2023-02-02T00:00:00&#x27;,\n", "       &#x27;2023-01-21T00:00:00&#x27;, &#x27;2023-02-02T00:00:00&#x27;, &#x27;2023-02-14T00:00:00&#x27;,\n", "       &#x27;2023-02-02T00:00:00&#x27;, &#x27;2023-02-14T00:00:00&#x27;, &#x27;2023-02-26T00:00:00&#x27;,\n", "       &#x27;2023-02-14T00:00:00&#x27;, &#x27;2023-02-26T00:00:00&#x27;, &#x27;2023-03-10T00:00:00&#x27;,\n", "       &#x27;2023-02-26T00:00:00&#x27;, &#x27;2023-03-10T00:00:00&#x27;, &#x27;2023-03-22T00:00:00&#x27;,\n", "       &#x27;2023-03-10T00:00:00&#x27;, &#x27;2023-03-22T00:00:00&#x27;, &#x27;2023-04-03T00:00:00&#x27;,\n", "       &#x27;2023-03-22T00:00:00&#x27;, &#x27;2023-04-03T00:00:00&#x27;, &#x27;2023-04-15T00:00:00&#x27;,\n", "       &#x27;2023-04-03T00:00:00&#x27;, &#x27;2023-04-15T00:00:00&#x27;, &#x27;2023-04-27T00:00:00&#x27;,\n", "       &#x27;2023-04-15T00:00:00&#x27;, &#x27;2023-04-27T00:00:00&#x27;, &#x27;2023-05-09T00:00:00&#x27;,\n", "       &#x27;2023-04-27T00:00:00&#x27;, &#x27;2023-05-09T00:00:00&#x27;, &#x27;2023-05-21T00:00:00&#x27;,\n", "       &#x27;2023-05-09T00:00:00&#x27;, &#x27;2023-05-21T00:00:00&#x27;, &#x27;2023-06-02T00:00:00&#x27;,\n", "       &#x27;2023-05-21T00:00:00&#x27;, &#x27;2023-06-02T00:00:00&#x27;, &#x27;2023-06-14T00:00:00&#x27;,\n", "       &#x27;2023-06-02T00:00:00&#x27;, &#x27;2023-06-14T00:00:00&#x27;, &#x27;2023-06-26T00:00:00&#x27;,\n", "       &#x27;2023-06-14T00:00:00&#x27;, &#x27;2023-06-26T00:00:00&#x27;, &#x27;2023-07-08T00:00:00&#x27;,\n", "       &#x27;2023-06-26T00:00:00&#x27;, &#x27;2023-07-08T00:00:00&#x27;, &#x27;2023-07-20T00:00:00&#x27;,\n", "       &#x27;2023-07-08T00:00:00&#x27;, &#x27;2023-07-20T00:00:00&#x27;, &#x27;2023-08-01T00:00:00&#x27;,\n", "       &#x27;2023-07-20T00:00:00&#x27;, &#x27;2023-08-01T00:00:00&#x27;, &#x27;2023-08-13T00:00:00&#x27;,\n", "       &#x27;2023-08-01T00:00:00&#x27;, &#x27;2023-08-13T00:00:00&#x27;, &#x27;2023-08-25T00:00:00&#x27;,\n", "       &#x27;2023-08-13T00:00:00&#x27;, &#x27;2023-08-25T00:00:00&#x27;, &#x27;2023-09-06T00:00:00&#x27;,\n", "       &#x27;2023-08-25T00:00:00&#x27;, &#x27;2023-09-06T00:00:00&#x27;, &#x27;2023-09-18T00:00:00&#x27;,\n", "       &#x27;2023-09-06T00:00:00&#x27;, &#x27;2023-09-18T00:00:00&#x27;, &#x27;2023-09-30T00:00:00&#x27;,\n", "       &#x27;2023-09-18T00:00:00&#x27;, &#x27;2023-09-30T00:00:00&#x27;, &#x27;2023-10-12T00:00:00&#x27;,\n", "       &#x27;2023-09-30T00:00:00&#x27;, &#x27;2023-10-12T00:00:00&#x27;, &#x27;2023-10-24T00:00:00&#x27;,\n", "       &#x27;2023-10-12T00:00:00&#x27;, &#x27;2023-10-24T00:00:00&#x27;, &#x27;2023-11-05T00:00:00&#x27;,\n", "       &#x27;2023-10-24T00:00:00&#x27;, &#x27;2023-11-05T00:00:00&#x27;, &#x27;2023-11-17T00:00:00&#x27;,\n", "       &#x27;2023-11-05T00:00:00&#x27;, &#x27;2023-11-17T00:00:00&#x27;, &#x27;2023-11-29T00:00:00&#x27;,\n", "       &#x27;2023-11-17T00:00:00&#x27;, &#x27;2023-11-29T00:00:00&#x27;, &#x27;2023-12-11T00:00:00&#x27;,\n", "       &#x27;2023-11-29T00:00:00&#x27;, &#x27;2023-12-11T00:00:00&#x27;, &#x27;2023-12-23T00:00:00&#x27;,\n", "       &#x27;2023-12-11T00:00:00&#x27;, &#x27;2023-12-23T00:00:00&#x27;, &#x27;2024-01-04T00:00:00&#x27;,\n", "       &#x27;2023-12-23T00:00:00&#x27;, &#x27;2024-01-04T00:00:00&#x27;, &#x27;2024-01-16T00:00:00&#x27;,\n", "       &#x27;2024-01-04T00:00:00&#x27;, &#x27;2024-01-16T00:00:00&#x27;, &#x27;2024-01-28T00:00:00&#x27;,\n", "       &#x27;2024-01-16T00:00:00&#x27;, &#x27;2024-01-28T00:00:00&#x27;, &#x27;2024-02-09T00:00:00&#x27;,\n", "       &#x27;2024-01-28T00:00:00&#x27;, &#x27;2024-02-09T00:00:00&#x27;, &#x27;2024-02-21T00:00:00&#x27;,\n", "       &#x27;2024-02-09T00:00:00&#x27;, &#x27;2024-02-21T00:00:00&#x27;, &#x27;2024-03-04T00:00:00&#x27;,\n", "       &#x27;2024-02-21T00:00:00&#x27;, &#x27;2024-03-04T00:00:00&#x27;, &#x27;2024-03-04T00:00:00&#x27;],\n", "      dtype=&#x27;datetime64[s]&#x27;)</pre></div></div></li><li class='xr-section-item'><input id='section-f14afa55-eeb7-42ba-a7ef-148273588ab8' class='xr-section-summary-in' type='checkbox'  checked><label for='section-f14afa55-eeb7-42ba-a7ef-148273588ab8' class='xr-section-summary' >Statistic: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><div style=\"width: 100%; margin: 0px 0px 0px 1em;\">\n", "  <table style=\"margin: 5px 2.5%; width: 95%;\">\n", "    <body>\n", "      <tr>\n", "        <th>start</th>\n", "        <td>2022-01-14</td>\n", "      </tr>\n", "      <tr>\n", "        <th>end</th>\n", "        <td>2024-03-04</td>\n", "      </tr>\n", "      <tr>\n", "        <th>unique</th>\n", "        <td>66</td>\n", "      </tr>\n", "      <tr>\n", "        <th>total</th>\n", "        <td>195</td>\n", "      </tr>\n", "    </body>\n", "  </table>\n", "</div></div></li></ul></div></div></div>\n", "  </li><li class=\"xr-var-item\"><div class=\"xr-var-name\"><span class=\"xr-has-index\">days</span></div><div class=\"xr-var-dtype\">DaySpan</div><div class=\"xr-var-preview xr-preview\">12 24 36 12 24 36 12 24 ... 36 12 24 36 12 24 12</div>\n", "    <input class=\"xr-var-attrs-in\" disabled=\"disabled\" id=\"attrs-3504eb07-235d-4a78-81dc-d0c9b04ca42a\" type=\"checkbox\"><label for=\"attrs-3504eb07-235d-4a78-81dc-d0c9b04ca42a\" title=\"Show/Hide attributes\"><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label>\n", "    <input class=\"xr-var-data-in\" id=\"data-244a0aff-c253-4e47-8467-aef06b1f9bcb\" type=\"checkbox\"><label for=\"data-244a0aff-c253-4e47-8467-aef06b1f9bcb\" title=\"Show/Hide data repr\"><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class=\"xr-var-attrs\"><div class=\"xr-var-attrs\">\n", "        <dl class=\"xr-attrs\"></dl>\n", "      </div></div><div class=\"xr-var-data\"><div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>DaySpan([12, 24, 36, 12, 24, 36, 12, 24, 36, 12,\n", "         ...\n", "         36, 12, 24, 36, 12, 24, 36, 12, 24, 12],\n", "        dtype=&#x27;int32&#x27;, length=195)</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>faninsar.DaySpan</div><div class='xr-array-name'></div><ul class='xr-dim-list'><li><span>days</span>: 195</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-2f432d5b-d8b9-437f-ad11-2abc8f769287' class='xr-array-in' type='checkbox' checked><label for='section-2f432d5b-d8b9-437f-ad11-2abc8f769287' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>12 24 36 12 24 36 12 24 36 12 24 ... 24 36 12 24 36 12 24 36 12 24 12</span></div><div class='xr-array-data'><pre>array([12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36,\n", "       12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36,\n", "       12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36,\n", "       12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36,\n", "       12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36,\n", "       12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36,\n", "       12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36,\n", "       12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36,\n", "       12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36,\n", "       12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36,\n", "       12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 12], dtype=int32)</pre></div></div></li><li class='xr-section-item'><input id='section-4b28d5fe-e524-4f7d-86d6-4040309f1c69' class='xr-section-summary-in' type='checkbox'  checked><label for='section-4b28d5fe-e524-4f7d-86d6-4040309f1c69' class='xr-section-summary' >Statistic: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><div style=\"width: 100%; margin: 0px 0px 0px 1em;\">\n", "  <table style=\"margin: 5px 2.5%; width: 95%;\">\n", "    <body>\n", "      <tr>\n", "        <th>min</th>\n", "        <td>12</td>\n", "      </tr>\n", "      <tr>\n", "        <th>max</th>\n", "        <td>36</td>\n", "      </tr>\n", "      <tr>\n", "        <th>unique</th>\n", "        <td>3</td>\n", "      </tr>\n", "      <tr>\n", "        <th>total</th>\n", "        <td>195</td>\n", "      </tr>\n", "    </body>\n", "  </table>\n", "</div></div></li></ul></div></div></div>\n", "  </li><li class=\"xr-var-item\"><div class=\"xr-var-name\"><span class=\"xr-has-index\">dates</span></div><div class=\"xr-var-dtype\">Acquisition</div><div class=\"xr-var-preview xr-preview\">2022-01-02 2022-01-14 ... 2024-02-21 2024-03-04</div>\n", "    <input class=\"xr-var-attrs-in\" disabled=\"disabled\" id=\"attrs-29d7db62-5d9b-41a6-87f8-6d08bae388c5\" type=\"checkbox\"><label for=\"attrs-29d7db62-5d9b-41a6-87f8-6d08bae388c5\" title=\"Show/Hide attributes\"><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label>\n", "    <input class=\"xr-var-data-in\" id=\"data-cb26bf60-612b-4917-8fc6-4dc2de56a780\" type=\"checkbox\"><label for=\"data-cb26bf60-612b-4917-8fc6-4dc2de56a780\" title=\"Show/Hide data repr\"><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class=\"xr-var-attrs\"><div class=\"xr-var-attrs\">\n", "        <dl class=\"xr-attrs\"></dl>\n", "      </div></div><div class=\"xr-var-data\"><div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>Acquisition([&#x27;2022-01-02&#x27;, &#x27;2022-01-14&#x27;, &#x27;2022-01-26&#x27;, &#x27;2022-02-07&#x27;,\n", "             &#x27;2022-02-19&#x27;, &#x27;2022-03-03&#x27;, &#x27;2022-03-15&#x27;, &#x27;2022-03-27&#x27;,\n", "             &#x27;2022-04-08&#x27;, &#x27;2022-04-20&#x27;, &#x27;2022-05-02&#x27;, &#x27;2022-05-14&#x27;,\n", "             &#x27;2022-05-26&#x27;, &#x27;2022-06-07&#x27;, &#x27;2022-06-19&#x27;, &#x27;2022-07-01&#x27;,\n", "             &#x27;2022-07-13&#x27;, &#x27;2022-07-25&#x27;, &#x27;2022-08-06&#x27;, &#x27;2022-08-18&#x27;,\n", "             &#x27;2022-08-30&#x27;, &#x27;2022-09-11&#x27;, &#x27;2022-09-23&#x27;, &#x27;2022-10-05&#x27;,\n", "             &#x27;2022-10-17&#x27;, &#x27;2022-10-29&#x27;, &#x27;2022-11-10&#x27;, &#x27;2022-11-22&#x27;,\n", "             &#x27;2022-12-04&#x27;, &#x27;2022-12-16&#x27;, &#x27;2022-12-28&#x27;, &#x27;2023-01-09&#x27;,\n", "             &#x27;2023-01-21&#x27;, &#x27;2023-02-02&#x27;, &#x27;2023-02-14&#x27;, &#x27;2023-02-26&#x27;,\n", "             &#x27;2023-03-10&#x27;, &#x27;2023-03-22&#x27;, &#x27;2023-04-03&#x27;, &#x27;2023-04-15&#x27;,\n", "             &#x27;2023-04-27&#x27;, &#x27;2023-05-09&#x27;, &#x27;2023-05-21&#x27;, &#x27;2023-06-02&#x27;,\n", "             &#x27;2023-06-14&#x27;, &#x27;2023-06-26&#x27;, &#x27;2023-07-08&#x27;, &#x27;2023-07-20&#x27;,\n", "             &#x27;2023-08-01&#x27;, &#x27;2023-08-13&#x27;, &#x27;2023-08-25&#x27;, &#x27;2023-09-06&#x27;,\n", "             &#x27;2023-09-18&#x27;, &#x27;2023-09-30&#x27;, &#x27;2023-10-12&#x27;, &#x27;2023-10-24&#x27;,\n", "             &#x27;2023-11-05&#x27;, &#x27;2023-11-17&#x27;, &#x27;2023-11-29&#x27;, &#x27;2023-12-11&#x27;,\n", "             &#x27;2023-12-23&#x27;, &#x27;2024-01-04&#x27;, &#x27;2024-01-16&#x27;, &#x27;2024-01-28&#x27;,\n", "             &#x27;2024-02-09&#x27;, &#x27;2024-02-21&#x27;, &#x27;2024-03-04&#x27;],\n", "            dtype=&#x27;datetime64[s]&#x27;, freq=None)</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>faninsar.Acquisition</div><div class='xr-array-name'></div><ul class='xr-dim-list'><li><span>dates</span>: 67</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-3e13f7a0-9c67-487e-8736-c078a775cfed' class='xr-array-in' type='checkbox' checked><label for='section-3e13f7a0-9c67-487e-8736-c078a775cfed' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>2022-01-02 2022-01-14 2022-01-26 ... 2024-02-09 2024-02-21 2024-03-04</span></div><div class='xr-array-data'><pre>array([&#x27;2022-01-02T00:00:00&#x27;, &#x27;2022-01-14T00:00:00&#x27;, &#x27;2022-01-26T00:00:00&#x27;,\n", "       &#x27;2022-02-07T00:00:00&#x27;, &#x27;2022-02-19T00:00:00&#x27;, &#x27;2022-03-03T00:00:00&#x27;,\n", "       &#x27;2022-03-15T00:00:00&#x27;, &#x27;2022-03-27T00:00:00&#x27;, &#x27;2022-04-08T00:00:00&#x27;,\n", "       &#x27;2022-04-20T00:00:00&#x27;, &#x27;2022-05-02T00:00:00&#x27;, &#x27;2022-05-14T00:00:00&#x27;,\n", "       &#x27;2022-05-26T00:00:00&#x27;, &#x27;2022-06-07T00:00:00&#x27;, &#x27;2022-06-19T00:00:00&#x27;,\n", "       &#x27;2022-07-01T00:00:00&#x27;, &#x27;2022-07-13T00:00:00&#x27;, &#x27;2022-07-25T00:00:00&#x27;,\n", "       &#x27;2022-08-06T00:00:00&#x27;, &#x27;2022-08-18T00:00:00&#x27;, &#x27;2022-08-30T00:00:00&#x27;,\n", "       &#x27;2022-09-11T00:00:00&#x27;, &#x27;2022-09-23T00:00:00&#x27;, &#x27;2022-10-05T00:00:00&#x27;,\n", "       &#x27;2022-10-17T00:00:00&#x27;, &#x27;2022-10-29T00:00:00&#x27;, &#x27;2022-11-10T00:00:00&#x27;,\n", "       &#x27;2022-11-22T00:00:00&#x27;, &#x27;2022-12-04T00:00:00&#x27;, &#x27;2022-12-16T00:00:00&#x27;,\n", "       &#x27;2022-12-28T00:00:00&#x27;, &#x27;2023-01-09T00:00:00&#x27;, &#x27;2023-01-21T00:00:00&#x27;,\n", "       &#x27;2023-02-02T00:00:00&#x27;, &#x27;2023-02-14T00:00:00&#x27;, &#x27;2023-02-26T00:00:00&#x27;,\n", "       &#x27;2023-03-10T00:00:00&#x27;, &#x27;2023-03-22T00:00:00&#x27;, &#x27;2023-04-03T00:00:00&#x27;,\n", "       &#x27;2023-04-15T00:00:00&#x27;, &#x27;2023-04-27T00:00:00&#x27;, &#x27;2023-05-09T00:00:00&#x27;,\n", "       &#x27;2023-05-21T00:00:00&#x27;, &#x27;2023-06-02T00:00:00&#x27;, &#x27;2023-06-14T00:00:00&#x27;,\n", "       &#x27;2023-06-26T00:00:00&#x27;, &#x27;2023-07-08T00:00:00&#x27;, &#x27;2023-07-20T00:00:00&#x27;,\n", "       &#x27;2023-08-01T00:00:00&#x27;, &#x27;2023-08-13T00:00:00&#x27;, &#x27;2023-08-25T00:00:00&#x27;,\n", "       &#x27;2023-09-06T00:00:00&#x27;, &#x27;2023-09-18T00:00:00&#x27;, &#x27;2023-09-30T00:00:00&#x27;,\n", "       &#x27;2023-10-12T00:00:00&#x27;, &#x27;2023-10-24T00:00:00&#x27;, &#x27;2023-11-05T00:00:00&#x27;,\n", "       &#x27;2023-11-17T00:00:00&#x27;, &#x27;2023-11-29T00:00:00&#x27;, &#x27;2023-12-11T00:00:00&#x27;,\n", "       &#x27;2023-12-23T00:00:00&#x27;, &#x27;2024-01-04T00:00:00&#x27;, &#x27;2024-01-16T00:00:00&#x27;,\n", "       &#x27;2024-01-28T00:00:00&#x27;, &#x27;2024-02-09T00:00:00&#x27;, &#x27;2024-02-21T00:00:00&#x27;,\n", "       &#x27;2024-03-04T00:00:00&#x27;], dtype=&#x27;datetime64[s]&#x27;)</pre></div></div></li><li class='xr-section-item'><input id='section-f309c271-dd70-45aa-b3e7-a0436c0a4358' class='xr-section-summary-in' type='checkbox'  checked><label for='section-f309c271-dd70-45aa-b3e7-a0436c0a4358' class='xr-section-summary' >Statistic: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><div style=\"width: 100%; margin: 0px 0px 0px 1em;\">\n", "  <table style=\"margin: 5px 2.5%; width: 95%;\">\n", "    <body>\n", "      <tr>\n", "        <th>start</th>\n", "        <td>2022-01-02</td>\n", "      </tr>\n", "      <tr>\n", "        <th>end</th>\n", "        <td>2024-03-04</td>\n", "      </tr>\n", "      <tr>\n", "        <th>unique</th>\n", "        <td>67</td>\n", "      </tr>\n", "      <tr>\n", "        <th>total</th>\n", "        <td>67</td>\n", "      </tr>\n", "    </body>\n", "  </table>\n", "</div></div></li></ul></div></div></div>\n", "  </li></ul></div></li></ul></div></div>"], "text/plain": ["         Pairs           \n", "       primary  secondary\n", "0   2022-01-02 2022-01-14\n", "1   2022-01-02 2022-01-26\n", "2   2022-01-02 2022-02-07\n", "3   2022-01-14 2022-01-26\n", "4   2022-01-14 2022-02-07\n", "..         ...        ...\n", "190 2024-01-28 2024-02-21\n", "191 2024-01-28 2024-03-04\n", "192 2024-02-09 2024-02-21\n", "193 2024-02-09 2024-03-04\n", "194 2024-02-21 2024-03-04\n", "\n", "[195 rows x 2 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["pairs[\"2022\":\"2024-03-15\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Filter Pairs by custom condition\n", "\n", "\n", "For example, if you want to keep only pairs that month of primary dates are in [8,9], you can achieve this by using the following code:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>        Pairs           \n", "      primary  secondary\n", "0  2020-08-04 2020-08-16\n", "1  2020-08-04 2020-08-28\n", "2  2020-08-04 2020-09-09\n", "3  2020-08-16 2020-08-28\n", "4  2020-08-16 2020-09-09\n", "..        ...        ...\n", "73 2024-09-12 2024-10-06\n", "74 2024-09-12 2024-10-18\n", "75 2024-09-24 2024-10-06\n", "76 2024-09-24 2024-10-18\n", "77 2024-09-24 2024-10-30\n", "\n", "[78 rows x 2 columns]</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>faninsar.Pairs</div><div class='xr-array-name'></div><ul class=\"xr-dim-list\"><li><span class=\"xr-has-index\">pairs</span>=78</li><li><span class=\"xr-has-index\">dates</span>=41</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-06bfad08-99c8-4c76-bf1d-b5f9e51b5152' class='xr-array-in' type='checkbox' checked><label for='section-06bfad08-99c8-4c76-bf1d-b5f9e51b5152' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>faninsar.Pairs<pairs=78,dates=41></span></div><div class='xr-array-data'><table><td><table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: center;\">\n", "      <th></th>\n", "      <th>primary</th>\n", "      <th>secondary</th>\n", "      <th>days</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2020-08-04</td>\n", "      <td>2020-08-16</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2020-08-04</td>\n", "      <td>2020-08-28</td>\n", "      <td>24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>76</th>\n", "      <td>2024-09-24</td>\n", "      <td>2024-10-18</td>\n", "      <td>24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>2024-09-24</td>\n", "      <td>2024-10-30</td>\n", "      <td>36</td>\n", "    </tr>\n", "  </tbody>\n", "</table></td><td><svg height=\"200\" width=\"200\">\n", "  <rect fill=\"#8B4903A0\" height=\"150.0\" width=\"80\" x=\"10\" y=\"25\"></rect>\n", "  <rect fill=\"#8B4903A0\" height=\"150.0\" width=\"80\" x=\"110\" y=\"25\"></rect>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"1\" x1=\"10\" x2=\"90\" y1=\"25.0\" y2=\"25.0\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"1\" x1=\"110\" x2=\"190\" y1=\"25.0\" y2=\"25.0\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"38.63636363636364\" y2=\"38.63636363636364\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"38.63636363636364\" y2=\"38.63636363636364\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"52.27272727272727\" y2=\"52.27272727272727\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"52.27272727272727\" y2=\"52.27272727272727\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"65.9090909090909\" y2=\"65.9090909090909\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"65.9090909090909\" y2=\"65.9090909090909\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"79.54545454545455\" y2=\"79.54545454545455\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"79.54545454545455\" y2=\"79.54545454545455\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"93.18181818181819\" y2=\"93.18181818181819\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"93.18181818181819\" y2=\"93.18181818181819\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"106.81818181818181\" y2=\"106.81818181818181\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"106.81818181818181\" y2=\"106.81818181818181\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"120.45454545454545\" y2=\"120.45454545454545\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"120.45454545454545\" y2=\"120.45454545454545\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"134.0909090909091\" y2=\"134.0909090909091\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"134.0909090909091\" y2=\"134.0909090909091\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"147.72727272727275\" y2=\"147.72727272727275\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"147.72727272727275\" y2=\"147.72727272727275\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"10\" x2=\"90\" y1=\"161.36363636363637\" y2=\"161.36363636363637\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"0.75\" x1=\"110\" x2=\"190\" y1=\"161.36363636363637\" y2=\"161.36363636363637\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"1\" x1=\"10\" x2=\"90\" y1=\"175.0\" y2=\"175.0\"></line>\n", "  <line stroke=\"#9e9e9eA0\" stroke-width=\"1\" x1=\"110\" x2=\"190\" y1=\"175.0\" y2=\"175.0\"></line>\n", "  <text style=\"font-size:0.85em; font-weight:bold; text-anchor:middle;\" x=\"50\" y=\"197\">primary</text>\n", "  <text style=\"font-size:0.85em; font-weight:bold; text-anchor:middle;\" x=\"150\" y=\"197\">secondary</text>\n", "  <text style=\"font-size:0.9em; font-weight:bold; text-anchor:middle;\" x=\"100\" y=\"15\">78 Pairs</text>\n", "</svg></td></table></div></div></li><li class='xr-section-item'><input id='section-3678fbac-cbb2-4053-8b6f-3b83f69b5e26' class='xr-section-summary-in' type='checkbox'  ><label for='section-3678fbac-cbb2-4053-8b6f-3b83f69b5e26' class='xr-section-summary' >Indexes: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class=\"xr-var-list\"><li class=\"xr-var-item\"><div class=\"xr-var-name\"><span class=\"xr-has-index\">primary</span></div><div class=\"xr-var-dtype\">Acquisition</div><div class=\"xr-var-preview xr-preview\">2020-08-04 2020-08-04 ... 2024-09-24 2024-09-24</div>\n", "    <input class=\"xr-var-attrs-in\" disabled=\"disabled\" id=\"attrs-86249c35-5570-44c9-b8a9-8e653d29c0c2\" type=\"checkbox\"><label for=\"attrs-86249c35-5570-44c9-b8a9-8e653d29c0c2\" title=\"Show/Hide attributes\"><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label>\n", "    <input class=\"xr-var-data-in\" id=\"data-9a83d773-b97c-4e1d-a6bc-60761594f764\" type=\"checkbox\"><label for=\"data-9a83d773-b97c-4e1d-a6bc-60761594f764\" title=\"Show/Hide data repr\"><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class=\"xr-var-attrs\"><div class=\"xr-var-attrs\">\n", "        <dl class=\"xr-attrs\"></dl>\n", "      </div></div><div class=\"xr-var-data\"><div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>Acquisition([&#x27;2020-08-04&#x27;, &#x27;2020-08-04&#x27;, &#x27;2020-08-04&#x27;, &#x27;2020-08-16&#x27;,\n", "             &#x27;2020-08-16&#x27;, &#x27;2020-08-16&#x27;, &#x27;2020-08-28&#x27;, &#x27;2020-08-28&#x27;,\n", "             &#x27;2020-08-28&#x27;, &#x27;2020-09-09&#x27;, &#x27;2020-09-09&#x27;, &#x27;2020-09-09&#x27;,\n", "             &#x27;2020-09-21&#x27;, &#x27;2020-09-21&#x27;, &#x27;2020-09-21&#x27;, &#x27;2021-08-11&#x27;,\n", "             &#x27;2021-08-11&#x27;, &#x27;2021-08-11&#x27;, &#x27;2021-08-23&#x27;, &#x27;2021-08-23&#x27;,\n", "             &#x27;2021-08-23&#x27;, &#x27;2021-09-04&#x27;, &#x27;2021-09-04&#x27;, &#x27;2021-09-04&#x27;,\n", "             &#x27;2021-09-16&#x27;, &#x27;2021-09-16&#x27;, &#x27;2021-09-16&#x27;, &#x27;2021-09-28&#x27;,\n", "             &#x27;2021-09-28&#x27;, &#x27;2021-09-28&#x27;, &#x27;2022-08-06&#x27;, &#x27;2022-08-06&#x27;,\n", "             &#x27;2022-08-06&#x27;, &#x27;2022-08-18&#x27;, &#x27;2022-08-18&#x27;, &#x27;2022-08-18&#x27;,\n", "             &#x27;2022-08-30&#x27;, &#x27;2022-08-30&#x27;, &#x27;2022-08-30&#x27;, &#x27;2022-09-11&#x27;,\n", "             &#x27;2022-09-11&#x27;, &#x27;2022-09-11&#x27;, &#x27;2022-09-23&#x27;, &#x27;2022-09-23&#x27;,\n", "             &#x27;2022-09-23&#x27;, &#x27;2023-08-01&#x27;, &#x27;2023-08-01&#x27;, &#x27;2023-08-01&#x27;,\n", "             &#x27;2023-08-13&#x27;, &#x27;2023-08-13&#x27;, &#x27;2023-08-13&#x27;, &#x27;2023-08-25&#x27;,\n", "             &#x27;2023-08-25&#x27;, &#x27;2023-08-25&#x27;, &#x27;2023-09-06&#x27;, &#x27;2023-09-06&#x27;,\n", "             &#x27;2023-09-06&#x27;, &#x27;2023-09-18&#x27;, &#x27;2023-09-18&#x27;, &#x27;2023-09-18&#x27;,\n", "             &#x27;2023-09-30&#x27;, &#x27;2023-09-30&#x27;, &#x27;2023-09-30&#x27;, &#x27;2024-08-07&#x27;,\n", "             &#x27;2024-08-07&#x27;, &#x27;2024-08-07&#x27;, &#x27;2024-08-19&#x27;, &#x27;2024-08-19&#x27;,\n", "             &#x27;2024-08-19&#x27;, &#x27;2024-08-31&#x27;, &#x27;2024-08-31&#x27;, &#x27;2024-08-31&#x27;,\n", "             &#x27;2024-09-12&#x27;, &#x27;2024-09-12&#x27;, &#x27;2024-09-12&#x27;, &#x27;2024-09-24&#x27;,\n", "             &#x27;2024-09-24&#x27;, &#x27;2024-09-24&#x27;],\n", "            dtype=&#x27;datetime64[s]&#x27;, freq=None)</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>faninsar.Acquisition</div><div class='xr-array-name'></div><ul class='xr-dim-list'><li><span>dates</span>: 78</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-7506c7ea-8bdd-44ae-8d07-c50f1f7413c9' class='xr-array-in' type='checkbox' checked><label for='section-7506c7ea-8bdd-44ae-8d07-c50f1f7413c9' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>2020-08-04 2020-08-04 2020-08-04 ... 2024-09-24 2024-09-24 2024-09-24</span></div><div class='xr-array-data'><pre>array([&#x27;2020-08-04T00:00:00&#x27;, &#x27;2020-08-04T00:00:00&#x27;, &#x27;2020-08-04T00:00:00&#x27;,\n", "       &#x27;2020-08-16T00:00:00&#x27;, &#x27;2020-08-16T00:00:00&#x27;, &#x27;2020-08-16T00:00:00&#x27;,\n", "       &#x27;2020-08-28T00:00:00&#x27;, &#x27;2020-08-28T00:00:00&#x27;, &#x27;2020-08-28T00:00:00&#x27;,\n", "       &#x27;2020-09-09T00:00:00&#x27;, &#x27;2020-09-09T00:00:00&#x27;, &#x27;2020-09-09T00:00:00&#x27;,\n", "       &#x27;2020-09-21T00:00:00&#x27;, &#x27;2020-09-21T00:00:00&#x27;, &#x27;2020-09-21T00:00:00&#x27;,\n", "       &#x27;2021-08-11T00:00:00&#x27;, &#x27;2021-08-11T00:00:00&#x27;, &#x27;2021-08-11T00:00:00&#x27;,\n", "       &#x27;2021-08-23T00:00:00&#x27;, &#x27;2021-08-23T00:00:00&#x27;, &#x27;2021-08-23T00:00:00&#x27;,\n", "       &#x27;2021-09-04T00:00:00&#x27;, &#x27;2021-09-04T00:00:00&#x27;, &#x27;2021-09-04T00:00:00&#x27;,\n", "       &#x27;2021-09-16T00:00:00&#x27;, &#x27;2021-09-16T00:00:00&#x27;, &#x27;2021-09-16T00:00:00&#x27;,\n", "       &#x27;2021-09-28T00:00:00&#x27;, &#x27;2021-09-28T00:00:00&#x27;, &#x27;2021-09-28T00:00:00&#x27;,\n", "       &#x27;2022-08-06T00:00:00&#x27;, &#x27;2022-08-06T00:00:00&#x27;, &#x27;2022-08-06T00:00:00&#x27;,\n", "       &#x27;2022-08-18T00:00:00&#x27;, &#x27;2022-08-18T00:00:00&#x27;, &#x27;2022-08-18T00:00:00&#x27;,\n", "       &#x27;2022-08-30T00:00:00&#x27;, &#x27;2022-08-30T00:00:00&#x27;, &#x27;2022-08-30T00:00:00&#x27;,\n", "       &#x27;2022-09-11T00:00:00&#x27;, &#x27;2022-09-11T00:00:00&#x27;, &#x27;2022-09-11T00:00:00&#x27;,\n", "       &#x27;2022-09-23T00:00:00&#x27;, &#x27;2022-09-23T00:00:00&#x27;, &#x27;2022-09-23T00:00:00&#x27;,\n", "       &#x27;2023-08-01T00:00:00&#x27;, &#x27;2023-08-01T00:00:00&#x27;, &#x27;2023-08-01T00:00:00&#x27;,\n", "       &#x27;2023-08-13T00:00:00&#x27;, &#x27;2023-08-13T00:00:00&#x27;, &#x27;2023-08-13T00:00:00&#x27;,\n", "       &#x27;2023-08-25T00:00:00&#x27;, &#x27;2023-08-25T00:00:00&#x27;, &#x27;2023-08-25T00:00:00&#x27;,\n", "       &#x27;2023-09-06T00:00:00&#x27;, &#x27;2023-09-06T00:00:00&#x27;, &#x27;2023-09-06T00:00:00&#x27;,\n", "       &#x27;2023-09-18T00:00:00&#x27;, &#x27;2023-09-18T00:00:00&#x27;, &#x27;2023-09-18T00:00:00&#x27;,\n", "       &#x27;2023-09-30T00:00:00&#x27;, &#x27;2023-09-30T00:00:00&#x27;, &#x27;2023-09-30T00:00:00&#x27;,\n", "       &#x27;2024-08-07T00:00:00&#x27;, &#x27;2024-08-07T00:00:00&#x27;, &#x27;2024-08-07T00:00:00&#x27;,\n", "       &#x27;2024-08-19T00:00:00&#x27;, &#x27;2024-08-19T00:00:00&#x27;, &#x27;2024-08-19T00:00:00&#x27;,\n", "       &#x27;2024-08-31T00:00:00&#x27;, &#x27;2024-08-31T00:00:00&#x27;, &#x27;2024-08-31T00:00:00&#x27;,\n", "       &#x27;2024-09-12T00:00:00&#x27;, &#x27;2024-09-12T00:00:00&#x27;, &#x27;2024-09-12T00:00:00&#x27;,\n", "       &#x27;2024-09-24T00:00:00&#x27;, &#x27;2024-09-24T00:00:00&#x27;, &#x27;2024-09-24T00:00:00&#x27;],\n", "      dtype=&#x27;datetime64[s]&#x27;)</pre></div></div></li><li class='xr-section-item'><input id='section-1dc76d2c-b916-4065-8f7a-ba786bd39fb4' class='xr-section-summary-in' type='checkbox'  checked><label for='section-1dc76d2c-b916-4065-8f7a-ba786bd39fb4' class='xr-section-summary' >Statistic: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><div style=\"width: 100%; margin: 0px 0px 0px 1em;\">\n", "  <table style=\"margin: 5px 2.5%; width: 95%;\">\n", "    <body>\n", "      <tr>\n", "        <th>start</th>\n", "        <td>2020-08-04</td>\n", "      </tr>\n", "      <tr>\n", "        <th>end</th>\n", "        <td>2024-09-24</td>\n", "      </tr>\n", "      <tr>\n", "        <th>unique</th>\n", "        <td>26</td>\n", "      </tr>\n", "      <tr>\n", "        <th>total</th>\n", "        <td>78</td>\n", "      </tr>\n", "    </body>\n", "  </table>\n", "</div></div></li></ul></div></div></div>\n", "  </li><li class=\"xr-var-item\"><div class=\"xr-var-name\"><span class=\"xr-has-index\">secondary</span></div><div class=\"xr-var-dtype\">Acquisition</div><div class=\"xr-var-preview xr-preview\">2020-08-16 2020-08-28 ... 2024-10-18 2024-10-30</div>\n", "    <input class=\"xr-var-attrs-in\" disabled=\"disabled\" id=\"attrs-73920a39-e7e1-47a2-8b80-5378eb2f1b77\" type=\"checkbox\"><label for=\"attrs-73920a39-e7e1-47a2-8b80-5378eb2f1b77\" title=\"Show/Hide attributes\"><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label>\n", "    <input class=\"xr-var-data-in\" id=\"data-7eb142c3-7b4e-4e27-9877-be44e632fd45\" type=\"checkbox\"><label for=\"data-7eb142c3-7b4e-4e27-9877-be44e632fd45\" title=\"Show/Hide data repr\"><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class=\"xr-var-attrs\"><div class=\"xr-var-attrs\">\n", "        <dl class=\"xr-attrs\"></dl>\n", "      </div></div><div class=\"xr-var-data\"><div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>Acquisition([&#x27;2020-08-16&#x27;, &#x27;2020-08-28&#x27;, &#x27;2020-09-09&#x27;, &#x27;2020-08-28&#x27;,\n", "             &#x27;2020-09-09&#x27;, &#x27;2020-09-21&#x27;, &#x27;2020-09-09&#x27;, &#x27;2020-09-21&#x27;,\n", "             &#x27;2020-10-03&#x27;, &#x27;2020-09-21&#x27;, &#x27;2020-10-03&#x27;, &#x27;2020-10-15&#x27;,\n", "             &#x27;2020-10-03&#x27;, &#x27;2020-10-15&#x27;, &#x27;2020-10-27&#x27;, &#x27;2021-08-23&#x27;,\n", "             &#x27;2021-09-04&#x27;, &#x27;2021-09-16&#x27;, &#x27;2021-09-04&#x27;, &#x27;2021-09-16&#x27;,\n", "             &#x27;2021-09-28&#x27;, &#x27;2021-09-16&#x27;, &#x27;2021-09-28&#x27;, &#x27;2021-10-10&#x27;,\n", "             &#x27;2021-09-28&#x27;, &#x27;2021-10-10&#x27;, &#x27;2021-10-22&#x27;, &#x27;2021-10-10&#x27;,\n", "             &#x27;2021-10-22&#x27;, &#x27;2021-11-03&#x27;, &#x27;2022-08-18&#x27;, &#x27;2022-08-30&#x27;,\n", "             &#x27;2022-09-11&#x27;, &#x27;2022-08-30&#x27;, &#x27;2022-09-11&#x27;, &#x27;2022-09-23&#x27;,\n", "             &#x27;2022-09-11&#x27;, &#x27;2022-09-23&#x27;, &#x27;2022-10-05&#x27;, &#x27;2022-09-23&#x27;,\n", "             &#x27;2022-10-05&#x27;, &#x27;2022-10-17&#x27;, &#x27;2022-10-05&#x27;, &#x27;2022-10-17&#x27;,\n", "             &#x27;2022-10-29&#x27;, &#x27;2023-08-13&#x27;, &#x27;2023-08-25&#x27;, &#x27;2023-09-06&#x27;,\n", "             &#x27;2023-08-25&#x27;, &#x27;2023-09-06&#x27;, &#x27;2023-09-18&#x27;, &#x27;2023-09-06&#x27;,\n", "             &#x27;2023-09-18&#x27;, &#x27;2023-09-30&#x27;, &#x27;2023-09-18&#x27;, &#x27;2023-09-30&#x27;,\n", "             &#x27;2023-10-12&#x27;, &#x27;2023-09-30&#x27;, &#x27;2023-10-12&#x27;, &#x27;2023-10-24&#x27;,\n", "             &#x27;2023-10-12&#x27;, &#x27;2023-10-24&#x27;, &#x27;2023-11-05&#x27;, &#x27;2024-08-19&#x27;,\n", "             &#x27;2024-08-31&#x27;, &#x27;2024-09-12&#x27;, &#x27;2024-08-31&#x27;, &#x27;2024-09-12&#x27;,\n", "             &#x27;2024-09-24&#x27;, &#x27;2024-09-12&#x27;, &#x27;2024-09-24&#x27;, &#x27;2024-10-06&#x27;,\n", "             &#x27;2024-09-24&#x27;, &#x27;2024-10-06&#x27;, &#x27;2024-10-18&#x27;, &#x27;2024-10-06&#x27;,\n", "             &#x27;2024-10-18&#x27;, &#x27;2024-10-30&#x27;],\n", "            dtype=&#x27;datetime64[s]&#x27;, freq=None)</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>faninsar.Acquisition</div><div class='xr-array-name'></div><ul class='xr-dim-list'><li><span>dates</span>: 78</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-302b638e-ded2-4ad6-aa44-4638989b3764' class='xr-array-in' type='checkbox' checked><label for='section-302b638e-ded2-4ad6-aa44-4638989b3764' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>2020-08-16 2020-08-28 2020-09-09 ... 2024-10-06 2024-10-18 2024-10-30</span></div><div class='xr-array-data'><pre>array([&#x27;2020-08-16T00:00:00&#x27;, &#x27;2020-08-28T00:00:00&#x27;, &#x27;2020-09-09T00:00:00&#x27;,\n", "       &#x27;2020-08-28T00:00:00&#x27;, &#x27;2020-09-09T00:00:00&#x27;, &#x27;2020-09-21T00:00:00&#x27;,\n", "       &#x27;2020-09-09T00:00:00&#x27;, &#x27;2020-09-21T00:00:00&#x27;, &#x27;2020-10-03T00:00:00&#x27;,\n", "       &#x27;2020-09-21T00:00:00&#x27;, &#x27;2020-10-03T00:00:00&#x27;, &#x27;2020-10-15T00:00:00&#x27;,\n", "       &#x27;2020-10-03T00:00:00&#x27;, &#x27;2020-10-15T00:00:00&#x27;, &#x27;2020-10-27T00:00:00&#x27;,\n", "       &#x27;2021-08-23T00:00:00&#x27;, &#x27;2021-09-04T00:00:00&#x27;, &#x27;2021-09-16T00:00:00&#x27;,\n", "       &#x27;2021-09-04T00:00:00&#x27;, &#x27;2021-09-16T00:00:00&#x27;, &#x27;2021-09-28T00:00:00&#x27;,\n", "       &#x27;2021-09-16T00:00:00&#x27;, &#x27;2021-09-28T00:00:00&#x27;, &#x27;2021-10-10T00:00:00&#x27;,\n", "       &#x27;2021-09-28T00:00:00&#x27;, &#x27;2021-10-10T00:00:00&#x27;, &#x27;2021-10-22T00:00:00&#x27;,\n", "       &#x27;2021-10-10T00:00:00&#x27;, &#x27;2021-10-22T00:00:00&#x27;, &#x27;2021-11-03T00:00:00&#x27;,\n", "       &#x27;2022-08-18T00:00:00&#x27;, &#x27;2022-08-30T00:00:00&#x27;, &#x27;2022-09-11T00:00:00&#x27;,\n", "       &#x27;2022-08-30T00:00:00&#x27;, &#x27;2022-09-11T00:00:00&#x27;, &#x27;2022-09-23T00:00:00&#x27;,\n", "       &#x27;2022-09-11T00:00:00&#x27;, &#x27;2022-09-23T00:00:00&#x27;, &#x27;2022-10-05T00:00:00&#x27;,\n", "       &#x27;2022-09-23T00:00:00&#x27;, &#x27;2022-10-05T00:00:00&#x27;, &#x27;2022-10-17T00:00:00&#x27;,\n", "       &#x27;2022-10-05T00:00:00&#x27;, &#x27;2022-10-17T00:00:00&#x27;, &#x27;2022-10-29T00:00:00&#x27;,\n", "       &#x27;2023-08-13T00:00:00&#x27;, &#x27;2023-08-25T00:00:00&#x27;, &#x27;2023-09-06T00:00:00&#x27;,\n", "       &#x27;2023-08-25T00:00:00&#x27;, &#x27;2023-09-06T00:00:00&#x27;, &#x27;2023-09-18T00:00:00&#x27;,\n", "       &#x27;2023-09-06T00:00:00&#x27;, &#x27;2023-09-18T00:00:00&#x27;, &#x27;2023-09-30T00:00:00&#x27;,\n", "       &#x27;2023-09-18T00:00:00&#x27;, &#x27;2023-09-30T00:00:00&#x27;, &#x27;2023-10-12T00:00:00&#x27;,\n", "       &#x27;2023-09-30T00:00:00&#x27;, &#x27;2023-10-12T00:00:00&#x27;, &#x27;2023-10-24T00:00:00&#x27;,\n", "       &#x27;2023-10-12T00:00:00&#x27;, &#x27;2023-10-24T00:00:00&#x27;, &#x27;2023-11-05T00:00:00&#x27;,\n", "       &#x27;2024-08-19T00:00:00&#x27;, &#x27;2024-08-31T00:00:00&#x27;, &#x27;2024-09-12T00:00:00&#x27;,\n", "       &#x27;2024-08-31T00:00:00&#x27;, &#x27;2024-09-12T00:00:00&#x27;, &#x27;2024-09-24T00:00:00&#x27;,\n", "       &#x27;2024-09-12T00:00:00&#x27;, &#x27;2024-09-24T00:00:00&#x27;, &#x27;2024-10-06T00:00:00&#x27;,\n", "       &#x27;2024-09-24T00:00:00&#x27;, &#x27;2024-10-06T00:00:00&#x27;, &#x27;2024-10-18T00:00:00&#x27;,\n", "       &#x27;2024-10-06T00:00:00&#x27;, &#x27;2024-10-18T00:00:00&#x27;, &#x27;2024-10-30T00:00:00&#x27;],\n", "      dtype=&#x27;datetime64[s]&#x27;)</pre></div></div></li><li class='xr-section-item'><input id='section-343bafb3-de91-4f62-abea-c276451934de' class='xr-section-summary-in' type='checkbox'  checked><label for='section-343bafb3-de91-4f62-abea-c276451934de' class='xr-section-summary' >Statistic: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><div style=\"width: 100%; margin: 0px 0px 0px 1em;\">\n", "  <table style=\"margin: 5px 2.5%; width: 95%;\">\n", "    <body>\n", "      <tr>\n", "        <th>start</th>\n", "        <td>2020-08-16</td>\n", "      </tr>\n", "      <tr>\n", "        <th>end</th>\n", "        <td>2024-10-30</td>\n", "      </tr>\n", "      <tr>\n", "        <th>unique</th>\n", "        <td>36</td>\n", "      </tr>\n", "      <tr>\n", "        <th>total</th>\n", "        <td>78</td>\n", "      </tr>\n", "    </body>\n", "  </table>\n", "</div></div></li></ul></div></div></div>\n", "  </li><li class=\"xr-var-item\"><div class=\"xr-var-name\"><span class=\"xr-has-index\">days</span></div><div class=\"xr-var-dtype\">DaySpan</div><div class=\"xr-var-preview xr-preview\">12 24 36 12 24 36 12 24 ... 36 12 24 36 12 24 36</div>\n", "    <input class=\"xr-var-attrs-in\" disabled=\"disabled\" id=\"attrs-b3a0e6ad-0d6e-4dac-af0e-29f66b7bd8af\" type=\"checkbox\"><label for=\"attrs-b3a0e6ad-0d6e-4dac-af0e-29f66b7bd8af\" title=\"Show/Hide attributes\"><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label>\n", "    <input class=\"xr-var-data-in\" id=\"data-872b69d6-2008-4d26-9107-afa43d9f3d91\" type=\"checkbox\"><label for=\"data-872b69d6-2008-4d26-9107-afa43d9f3d91\" title=\"Show/Hide data repr\"><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class=\"xr-var-attrs\"><div class=\"xr-var-attrs\">\n", "        <dl class=\"xr-attrs\"></dl>\n", "      </div></div><div class=\"xr-var-data\"><div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>DaySpan([12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24,\n", "         36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12,\n", "         24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36,\n", "         12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24,\n", "         36, 12, 24, 36, 12, 24, 36, 12, 24, 36],\n", "        dtype=&#x27;int32&#x27;)</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>faninsar.DaySpan</div><div class='xr-array-name'></div><ul class='xr-dim-list'><li><span>days</span>: 78</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-27943948-8cac-4924-840d-d71f9d363acf' class='xr-array-in' type='checkbox' checked><label for='section-27943948-8cac-4924-840d-d71f9d363acf' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>12 24 36 12 24 36 12 24 36 12 24 ... 24 36 12 24 36 12 24 36 12 24 36</span></div><div class='xr-array-data'><pre>array([12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36,\n", "       12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36,\n", "       12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36,\n", "       12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36, 12, 24, 36,\n", "       12, 24, 36, 12, 24, 36], dtype=int32)</pre></div></div></li><li class='xr-section-item'><input id='section-7de39e6d-7248-4ebf-a3f1-9501285e1b3b' class='xr-section-summary-in' type='checkbox'  checked><label for='section-7de39e6d-7248-4ebf-a3f1-9501285e1b3b' class='xr-section-summary' >Statistic: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><div style=\"width: 100%; margin: 0px 0px 0px 1em;\">\n", "  <table style=\"margin: 5px 2.5%; width: 95%;\">\n", "    <body>\n", "      <tr>\n", "        <th>min</th>\n", "        <td>12</td>\n", "      </tr>\n", "      <tr>\n", "        <th>max</th>\n", "        <td>36</td>\n", "      </tr>\n", "      <tr>\n", "        <th>unique</th>\n", "        <td>3</td>\n", "      </tr>\n", "      <tr>\n", "        <th>total</th>\n", "        <td>78</td>\n", "      </tr>\n", "    </body>\n", "  </table>\n", "</div></div></li></ul></div></div></div>\n", "  </li><li class=\"xr-var-item\"><div class=\"xr-var-name\"><span class=\"xr-has-index\">dates</span></div><div class=\"xr-var-dtype\">Acquisition</div><div class=\"xr-var-preview xr-preview\">2020-08-04 2020-08-16 ... 2024-10-18 2024-10-30</div>\n", "    <input class=\"xr-var-attrs-in\" disabled=\"disabled\" id=\"attrs-72cc0a86-4916-403e-b9b9-b027100ec7a0\" type=\"checkbox\"><label for=\"attrs-72cc0a86-4916-403e-b9b9-b027100ec7a0\" title=\"Show/Hide attributes\"><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label>\n", "    <input class=\"xr-var-data-in\" id=\"data-d4a7f2d1-66f3-4dad-8c72-00cdaead39f1\" type=\"checkbox\"><label for=\"data-d4a7f2d1-66f3-4dad-8c72-00cdaead39f1\" title=\"Show/Hide data repr\"><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class=\"xr-var-attrs\"><div class=\"xr-var-attrs\">\n", "        <dl class=\"xr-attrs\"></dl>\n", "      </div></div><div class=\"xr-var-data\"><div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>Acquisition([&#x27;2020-08-04&#x27;, &#x27;2020-08-16&#x27;, &#x27;2020-08-28&#x27;, &#x27;2020-09-09&#x27;,\n", "             &#x27;2020-09-21&#x27;, &#x27;2020-10-03&#x27;, &#x27;2020-10-15&#x27;, &#x27;2020-10-27&#x27;,\n", "             &#x27;2021-08-11&#x27;, &#x27;2021-08-23&#x27;, &#x27;2021-09-04&#x27;, &#x27;2021-09-16&#x27;,\n", "             &#x27;2021-09-28&#x27;, &#x27;2021-10-10&#x27;, &#x27;2021-10-22&#x27;, &#x27;2021-11-03&#x27;,\n", "             &#x27;2022-08-06&#x27;, &#x27;2022-08-18&#x27;, &#x27;2022-08-30&#x27;, &#x27;2022-09-11&#x27;,\n", "             &#x27;2022-09-23&#x27;, &#x27;2022-10-05&#x27;, &#x27;2022-10-17&#x27;, &#x27;2022-10-29&#x27;,\n", "             &#x27;2023-08-01&#x27;, &#x27;2023-08-13&#x27;, &#x27;2023-08-25&#x27;, &#x27;2023-09-06&#x27;,\n", "             &#x27;2023-09-18&#x27;, &#x27;2023-09-30&#x27;, &#x27;2023-10-12&#x27;, &#x27;2023-10-24&#x27;,\n", "             &#x27;2023-11-05&#x27;, &#x27;2024-08-07&#x27;, &#x27;2024-08-19&#x27;, &#x27;2024-08-31&#x27;,\n", "             &#x27;2024-09-12&#x27;, &#x27;2024-09-24&#x27;, &#x27;2024-10-06&#x27;, &#x27;2024-10-18&#x27;,\n", "             &#x27;2024-10-30&#x27;],\n", "            dtype=&#x27;datetime64[s]&#x27;, freq=None)</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>faninsar.Acquisition</div><div class='xr-array-name'></div><ul class='xr-dim-list'><li><span>dates</span>: 41</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-486b3f3b-6ed8-4a5c-817a-9bf46f3311cd' class='xr-array-in' type='checkbox' checked><label for='section-486b3f3b-6ed8-4a5c-817a-9bf46f3311cd' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>2020-08-04 2020-08-16 2020-08-28 ... 2024-10-06 2024-10-18 2024-10-30</span></div><div class='xr-array-data'><pre>array([&#x27;2020-08-04T00:00:00&#x27;, &#x27;2020-08-16T00:00:00&#x27;, &#x27;2020-08-28T00:00:00&#x27;,\n", "       &#x27;2020-09-09T00:00:00&#x27;, &#x27;2020-09-21T00:00:00&#x27;, &#x27;2020-10-03T00:00:00&#x27;,\n", "       &#x27;2020-10-15T00:00:00&#x27;, &#x27;2020-10-27T00:00:00&#x27;, &#x27;2021-08-11T00:00:00&#x27;,\n", "       &#x27;2021-08-23T00:00:00&#x27;, &#x27;2021-09-04T00:00:00&#x27;, &#x27;2021-09-16T00:00:00&#x27;,\n", "       &#x27;2021-09-28T00:00:00&#x27;, &#x27;2021-10-10T00:00:00&#x27;, &#x27;2021-10-22T00:00:00&#x27;,\n", "       &#x27;2021-11-03T00:00:00&#x27;, &#x27;2022-08-06T00:00:00&#x27;, &#x27;2022-08-18T00:00:00&#x27;,\n", "       &#x27;2022-08-30T00:00:00&#x27;, &#x27;2022-09-11T00:00:00&#x27;, &#x27;2022-09-23T00:00:00&#x27;,\n", "       &#x27;2022-10-05T00:00:00&#x27;, &#x27;2022-10-17T00:00:00&#x27;, &#x27;2022-10-29T00:00:00&#x27;,\n", "       &#x27;2023-08-01T00:00:00&#x27;, &#x27;2023-08-13T00:00:00&#x27;, &#x27;2023-08-25T00:00:00&#x27;,\n", "       &#x27;2023-09-06T00:00:00&#x27;, &#x27;2023-09-18T00:00:00&#x27;, &#x27;2023-09-30T00:00:00&#x27;,\n", "       &#x27;2023-10-12T00:00:00&#x27;, &#x27;2023-10-24T00:00:00&#x27;, &#x27;2023-11-05T00:00:00&#x27;,\n", "       &#x27;2024-08-07T00:00:00&#x27;, &#x27;2024-08-19T00:00:00&#x27;, &#x27;2024-08-31T00:00:00&#x27;,\n", "       &#x27;2024-09-12T00:00:00&#x27;, &#x27;2024-09-24T00:00:00&#x27;, &#x27;2024-10-06T00:00:00&#x27;,\n", "       &#x27;2024-10-18T00:00:00&#x27;, &#x27;2024-10-30T00:00:00&#x27;], dtype=&#x27;datetime64[s]&#x27;)</pre></div></div></li><li class='xr-section-item'><input id='section-67763a5e-6543-40ab-905f-572d659bb11d' class='xr-section-summary-in' type='checkbox'  checked><label for='section-67763a5e-6543-40ab-905f-572d659bb11d' class='xr-section-summary' >Statistic: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><div style=\"width: 100%; margin: 0px 0px 0px 1em;\">\n", "  <table style=\"margin: 5px 2.5%; width: 95%;\">\n", "    <body>\n", "      <tr>\n", "        <th>start</th>\n", "        <td>2020-08-04</td>\n", "      </tr>\n", "      <tr>\n", "        <th>end</th>\n", "        <td>2024-10-30</td>\n", "      </tr>\n", "      <tr>\n", "        <th>unique</th>\n", "        <td>41</td>\n", "      </tr>\n", "      <tr>\n", "        <th>total</th>\n", "        <td>41</td>\n", "      </tr>\n", "    </body>\n", "  </table>\n", "</div></div></li></ul></div></div></div>\n", "  </li></ul></div></li></ul></div></div>"], "text/plain": ["        Pairs           \n", "      primary  secondary\n", "0  2020-08-04 2020-08-16\n", "1  2020-08-04 2020-08-28\n", "2  2020-08-04 2020-09-09\n", "3  2020-08-16 2020-08-28\n", "4  2020-08-16 2020-09-09\n", "..        ...        ...\n", "73 2024-09-12 2024-10-06\n", "74 2024-09-12 2024-10-18\n", "75 2024-09-24 2024-10-06\n", "76 2024-09-24 2024-10-18\n", "77 2024-09-24 2024-10-30\n", "\n", "[78 rows x 2 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["mask = pairs.primary.month.map(lambda x: x in [8, 9])\n", "pairs[mask]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "geo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}