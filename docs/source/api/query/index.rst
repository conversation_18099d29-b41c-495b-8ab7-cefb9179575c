.. _query_module:

Queries
=======

Query in FanInSAR is used to retrieve or sample values from a GeoDataset. It consists of two main modules: :ref:`query` and :ref:`query_result`. These modules provide functionality for defining query conditions and storing the results of the query, respectively.

- :ref:`query`: This module is used to define various query conditions.
- :ref:`query_result`: This module is used to store the results of queries.


.. toctree::
   :maxdepth: 2
   :caption: Contents:

   query
   query_result
