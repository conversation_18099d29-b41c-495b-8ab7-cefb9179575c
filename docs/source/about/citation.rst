.. _citation:

================
Citing This Work
================

When citing FanInSAR, you can use `Zenodo DOI <https://zenodo.org/records/11398347>`_ for each release. Below is the example of resulting ``BiBTeX`` record for FanInSAR 0.0.1 and a reference using ``APA`` 6th ed.



.. code-block::
    :caption: APA 6th ed reference

    Fan, C., & Liu, L. (2024). FanInSAR: A Fancy InSAR time series library, in a Pythonic, fast, and flexible way (0.0.1). Zenodo. https://doi.org/10.5281/zenodo.11398347

.. code-block:: bibtex
    :caption: BiBTeX record

    @software{fan2024FanInSAR,
    author       = {<PERSON>, <PERSON> and
                    <PERSON>, <PERSON>},
    title        = {{FanInSAR: A Fancy InSAR time series library, in a Pythonic, fast, and flexible way}},
    month        = may,
    year         = 2024,
    publisher    = {Zenodo},
    version      = {0.0.1},
    doi          = {10.5281/zenodo.11398347},
    url          = {https://doi.org/10.5281/zenodo.11398347}
    }
