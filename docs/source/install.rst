Installation
============

FanInSAR is a Python package, and requires ``Python >= 3.8``. It can be installed using ``pip`` from PyPI:

.. code-block:: bash

    pip install FanInSAR

or from GitHub:

.. code-block:: bash

    pip install git+https://github.com/Fanchengyan/FanInSAR.git


.. Optional dependencies
.. ---------------------

.. The following dependencies are optional:

.. - isce3: for reading and writing ISCE products (TODO)
.. - zarr: for reading and writing Zarr products (TODO)
