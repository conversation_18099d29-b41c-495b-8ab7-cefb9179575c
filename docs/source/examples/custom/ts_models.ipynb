{"cells": [{"cell_type": "markdown", "id": "6555e7cd", "metadata": {}, "source": ["(custom_tsmodels)=\n", "\n", "# Custom Time Series Models\n", "\n", "In this tutorial, we will discuss how to customize your own time series models in ``FanInSAR``."]}, {"cell_type": "code", "execution_count": 9, "id": "24272fa1", "metadata": {}, "outputs": [], "source": ["from faninsar.NSBAS import TimeSeriesModels\n", "import numpy as np\n", "import pandas as pd\n", "from typing import Sequence, Literal\n", "from datetime import datetime\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "id": "0630daf0", "metadata": {}, "source": ["## Creating a Custom Model\n", "\n", "To create a custom model, you need to inherit from the `TimeSeriesModels` class. This class is an abstract base class that provides the fundamental framework for time series models. You are required to implement the `__init__` method to define model parameters and initialize its state.\n", "\n", "For simple mathematical models, it is usually sufficient to specify the observation times (`dates`) and the time unit (`unit`). For more complex models, additional parameters may be needed to support the time series modeling.\n", "\n", "As an example, we will create a simple cubic deformation time series model. The surface deformation {math}`d(t)` is mathematically expressed as:\n", "\n", "```{math}\n", "d(t) = a t^3 + b t^2 + c t + d\n", "```\n", "\n", "Here, `a`, `b`, `c`, and `d` are model parameters estimated from NSBAS inversion, and `t` represents time."]}, {"cell_type": "code", "execution_count": 2, "id": "d1fe4fef", "metadata": {}, "outputs": [], "source": ["class CubicModel(TimeSeriesModels):\n", "    \"\"\"Cubic model.\"\"\"\n", "\n", "    def __init__(\n", "        self,\n", "        dates: pd.DatetimeIndex | Sequence[datetime],\n", "        unit: Literal[\"year\", \"day\"] = \"day\",\n", "    ) -> None:\n", "        \"\"\"Initialize CubicModel.\n", "\n", "        Parameters\n", "        ----------\n", "        dates : pd.DatetimeIndex | Sequence[datetime]\n", "            Dates of SAR acquisitions. This can be easily obtained by accessing\n", "            :attr:`Pairs.dates <faninsar.Pairs.dates>`.\n", "        unit : Literal[\"year\", \"day\"], optional\n", "            Unit of day spans in time series model, by default \"day\".\n", "\n", "        \"\"\"\n", "        super().__init__(dates, unit=unit)\n", "        self._G_br = np.array(\n", "            [\n", "                self.date_spans**3,\n", "                self.date_spans**2,\n", "                self.date_spans,\n", "                np.ones_like(self.date_spans),\n", "            ],\n", "        ).T\n", "        self._param_names = [\"Rate of Change\", \"acceleration\", \"velocity\", \"constant\"]"]}, {"cell_type": "markdown", "id": "6864ffa8", "metadata": {}, "source": ["\n", "1. `super().__init__(dates, unit=unit)` is used to pass initialization arguments to the parent class `TimeSeriesModels`. This automatically processes the time series dates and unit, and creates the `dates`, `unit`, and `date_spans` attributes necessary for model construction.\n", "\n", "2. `date_spans` is computed from `dates` and `unit`, representing the time span of each date relative to the first date in the series. For simple mathematical models, it is usually sufficient to operate directly on `date_spans`.\n", "\n", "3. `self._G_br` is used to generate the bottom-right portion of the NSBAS inversion matrix $G$, which in this case is the matrix $[t^3, t^2, t, 1]$.\n", "\n", "4. `self._param_names` stores the names of the model parameters. We define them as `[\"Rate of Change\", \"Acceleration\", \"Velocity\", \"Constant\"]`, corresponding to the four coefficients of the cubic model.\n", "\n", "## Initializing the Model"]}, {"cell_type": "code", "execution_count": 6, "id": "e3838fdf", "metadata": {}, "outputs": [], "source": ["dates = pd.date_range(\"2020-01-01\", \"2020-12-31\", freq=\"12D\")\n", "\n", "cubic_model = CubicModel(dates, unit=\"year\")"]}, {"cell_type": "markdown", "id": "1c804b91", "metadata": {}, "source": ["The model can then be passed to the [NSBASMatrixFactory](#faninsar.NSBAS.inversion.NSBASMatrixFactory) class, which is responsible for constructing the design matrix $\\mathbf{G}$ and data vector $\\mathbf{d}$ used in the NSBAS inversion. For detailed instructions on how to use the `NSBASMatrixFactory` class, refer to the [Operate NSBAS inversion](https://faninsar.readthedocs.io/en/latest/user_guide/quick_start.html#operate-nsbas-inversion) section of the quick start guide.\n", "\n", "The bottom-right portion of the NSBAS inversion matrix $\\mathbf{G}$ can be accessed via the `G_br` method, while the corresponding parameter names are available through the `param_names` method. "]}, {"cell_type": "code", "execution_count": 16, "id": "5fe0de48", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Rate of Change', 'acceleration', 'velocity', 'constant']\n"]}], "source": ["names = cubic_model.param_names\n", "print(names)"]}, {"cell_type": "code", "execution_count": 7, "id": "e2c2746e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[0.00000000e+00 0.00000000e+00 0.00000000e+00 1.00000000e+00]\n", " [3.54628034e-05 1.07939908e-03 3.28542094e-02 1.00000000e+00]\n", " [2.83702427e-04 4.31759631e-03 6.57084189e-02 1.00000000e+00]\n", " [9.57495692e-04 9.71459170e-03 9.85626283e-02 1.00000000e+00]\n", " [2.26961942e-03 1.72703853e-02 1.31416838e-01 1.00000000e+00]\n", " [4.43285042e-03 2.69849770e-02 1.64271047e-01 1.00000000e+00]\n", " [7.65996553e-03 3.88583668e-02 1.97125257e-01 1.00000000e+00]\n", " [1.21637416e-02 5.28905548e-02 2.29979466e-01 1.00000000e+00]\n", " [1.81569553e-02 6.90815410e-02 2.62833676e-01 1.00000000e+00]\n", " [2.58523837e-02 8.74313253e-02 2.95687885e-01 1.00000000e+00]\n", " [3.54628034e-02 1.07939908e-01 3.28542094e-01 1.00000000e+00]\n", " [4.72009913e-02 1.30607288e-01 3.61396304e-01 1.00000000e+00]\n", " [6.12797243e-02 1.55433467e-01 3.94250513e-01 1.00000000e+00]\n", " [7.79117791e-02 1.82418444e-01 4.27104723e-01 1.00000000e+00]\n", " [9.73099325e-02 2.11562219e-01 4.59958932e-01 1.00000000e+00]\n", " [1.19686961e-01 2.42864793e-01 4.92813142e-01 1.00000000e+00]\n", " [1.45255643e-01 2.76326164e-01 5.25667351e-01 1.00000000e+00]\n", " [1.74228753e-01 3.11946334e-01 5.58521561e-01 1.00000000e+00]\n", " [2.06819069e-01 3.49725301e-01 5.91375770e-01 1.00000000e+00]\n", " [2.43239368e-01 3.89663067e-01 6.24229979e-01 1.00000000e+00]\n", " [2.83702427e-01 4.31759631e-01 6.57084189e-01 1.00000000e+00]\n", " [3.28421022e-01 4.76014994e-01 6.89938398e-01 1.00000000e+00]\n", " [3.77607931e-01 5.22429154e-01 7.22792608e-01 1.00000000e+00]\n", " [4.31475929e-01 5.71002112e-01 7.55646817e-01 1.00000000e+00]\n", " [4.90237794e-01 6.21733869e-01 7.88501027e-01 1.00000000e+00]\n", " [5.54106303e-01 6.74624424e-01 8.21355236e-01 1.00000000e+00]\n", " [6.23294232e-01 7.29673777e-01 8.54209446e-01 1.00000000e+00]\n", " [6.98014359e-01 7.86881928e-01 8.87063655e-01 1.00000000e+00]\n", " [7.78479460e-01 8.46248877e-01 9.19917864e-01 1.00000000e+00]\n", " [8.64902312e-01 9.07774625e-01 9.52772074e-01 1.00000000e+00]\n", " [9.57495692e-01 9.71459170e-01 9.85626283e-01 1.00000000e+00]]\n"]}], "source": ["print(cubic_model.G_br)"]}, {"cell_type": "markdown", "id": "255e1c93", "metadata": {}, "source": ["## Plotting model deformation\n", "\n", "If we get the model parameters $\\mathbf{m}$ after the NSBAS inversion, we can easily calculate the model deformation $d(t)$ using the $\\mathbf{m}$ vector and the `G_br` matrix. "]}, {"cell_type": "code", "execution_count": 30, "id": "c5874442", "metadata": {}, "outputs": [], "source": ["m = np.array([4, 3, 2, 1])\n", "d = cubic_model.G_br @ m"]}, {"cell_type": "code", "execution_count": 45, "id": "96918f02", "metadata": {}, "outputs": [{"data": {"image/png": "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****************************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", "text/plain": ["<Figure size 1800x1200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(6, 4), dpi=300)\n", "plt.plot(cubic_model.dates, d, \"o-\")\n", "plt.text(\n", "    0.2,\n", "    0.7,\n", "    f\"{names[0]}: {m[0]}\\n{names[1]}: {m[1]}\\n{names[2]}: {m[2]}\\n{names[3]}: {m[3]}\",\n", "    ha=\"center\",\n", "    va=\"center\",\n", "    transform=plt.gca().transAxes,\n", ")\n", "plt.text(\n", "    0.5,\n", "    0.9,\n", "    f\"$d(t) = {m[0]} * t^3 + {m[1]} * t^2 + {m[2]} * t + {m[3]}$\",\n", "    ha=\"center\",\n", "    va=\"center\",\n", "    transform=plt.gca().transAxes,\n", ")\n", "plt.xlabel(\"Date\")\n", "plt.ylabel(\"Deformation (mm)\")\n", "plt.title(\"Cubic Model\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "4ba210d5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "geo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}