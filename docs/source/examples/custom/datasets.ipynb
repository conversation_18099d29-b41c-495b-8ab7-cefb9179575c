{"cells": [{"cell_type": "markdown", "id": "c7bbdaed", "metadata": {}, "source": ["(custom_datasets)=\n", "\n", "# Custom Interferometric Datasets\n", "\n", "In this tutorial, we will walk through how to customize your own datasets in **FanInSAR**.\n", "\n", "Although we have provided several commonly used interferometric datasets, many datasets are still not yet implemented. We plan to add more in future releases, but you also have the option to implement your own dataset class.\n", "\n", "Fortunately, **FanInSAR** offers a simple and flexible interface for creating new interferometric dataset classes. By inheriting from the base class [`InterferogramDataset`](#faninsar.datasets.InterferogramDataset) and implementing a few essential methods, you can easily define a custom dataset.\n", "\n", "Below is a basic example demonstrating how to implement a dataset class for HyP3 Sentinel-1 data:"]}, {"cell_type": "code", "execution_count": 6, "id": "6ec7f5d5", "metadata": {}, "outputs": [], "source": ["from faninsar import Pairs\n", "from faninsar.datasets import InterferogramDataset\n", "from faninsar.constants import Sentinel1\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from pathlib import Path"]}, {"cell_type": "code", "execution_count": null, "id": "d51d7ce5", "metadata": {}, "outputs": [], "source": ["class HyP3S1(InterferogramDataset, Sentinel1):\n", "    \"\"\"A dataset manages the data of HyP3 Sentinel-1 product.\n", "\n", "    `Hyp3 <https://hyp3-docs.asf.alaska.edu/>`_ is a service for processing\n", "    Synthetic Aperture Radar (SAR) imagery. This class is used to manage the\n", "    data of Hyp3 product.\n", "    \"\"\"\n", "\n", "    pattern_unw = \"*unw_phase.tif\"\n", "    pattern_coh = \"*corr.tif\"\n", "\n", "    @classmethod\n", "    def parse_pairs(cls, paths: list[Path]) -> Pairs:\n", "        \"\"\"Parse the Pairs from the paths of the interferogram.\"\"\"\n", "        names = [Path(f).name for f in paths]\n", "        pair_names = [\"_\".join(i.split(\"_\")[1:3]) for i in names]\n", "        return Pairs.from_names(pair_names)\n"]}, {"cell_type": "markdown", "id": "a4435441", "metadata": {}, "source": ["\n", "* `pattern_unw` and `pattern_coh` are two patterns used to locate the unwrapped interferogram and coherence files, respectively.\n", "* The `parse_pairs` method extracts the image pair information from the interferogram file path.\n", "* In this example, the custom dataset class inherits from both `InterferogramDataset` and `Sentinel1`. The `Sentinel1` class provides properties such as wavelength and frequency specific to Sentinel-1 data.\n", "\n", "This example provides a simple implementation that should cover most common use cases. You can extend it by adding more methods and attributes as needed. You may also explore the source code of each dataset class by clicking `[source]` links under each class [here](#geo_datasets) to gain deeper insights and inspiration for your own implementation.\n"]}, {"cell_type": "code", "execution_count": null, "id": "f187173c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6609a003", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "geo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}