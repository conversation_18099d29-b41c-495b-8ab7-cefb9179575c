<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   viewBox="0 0 660 400"
   version="1.1"
   id="svg45"
   sodipodi:docname="remote_sensing_alignment.svg"
   width="660"
   height="400"
   inkscape:export-filename="remote_sensing_alignment.png"
   inkscape:export-xdpi="300"
   inkscape:export-ydpi="300"
   inkscape:version="1.4 (e7c3feb1, 2024-10-09)"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:svg="http://www.w3.org/2000/svg">
  <defs
     id="defs45" />
  <sodipodi:namedview
     id="namedview45"
     pagecolor="#ffffff"
     bordercolor="#000000"
     borderopacity="0.25"
     inkscape:showpageshadow="2"
     inkscape:pageopacity="0.0"
     inkscape:pagecheckerboard="false"
     inkscape:deskcolor="#d1d1d1"
     inkscape:clip-to-page="true"
     inkscape:export-bgcolor="#ffffffff"
     inkscape:zoom="0.63217123"
     inkscape:cx="244.39581"
     inkscape:cy="257.84154"
     inkscape:window-width="1512"
     inkscape:window-height="835"
     inkscape:window-x="0"
     inkscape:window-y="38"
     inkscape:window-maximized="0"
     inkscape:current-layer="svg45"
     labelstyle="default" />
  <!-- Background -->
  <rect
     width="800"
     height="400"
     fill="#f8f8f8"
     id="rect1"
     sodipodi:insensitive="true"
     x="0"
     y="0" />
  <!-- Left side - Before alignment -->
  <g
     transform="translate(8.669139,52.25)"
     id="g37">
    <!-- Original imagery - larger extent -->
    <rect
       x="0"
       y="0"
       width="300"
       height="300"
       fill="#a8dadc"
       stroke="#457b9d"
       stroke-width="2"
       id="rect2" />
    <!-- Original imagery grid - finer resolution -->
    <g
       stroke="#457b9d"
       stroke-width="0.5"
       stroke-dasharray="2, 2"
       id="g29"
       sodipodi:insensitive="true">
      <!-- Vertical grid lines - finer resolution -->
      <line
         x1="20"
         y1="0"
         x2="20"
         y2="300"
         id="line2" />
      <line
         x1="40"
         y1="0"
         x2="40"
         y2="300"
         id="line3" />
      <line
         x1="60"
         y1="0"
         x2="60"
         y2="300"
         id="line4" />
      <line
         x1="80"
         y1="0"
         x2="80"
         y2="300"
         id="line5" />
      <line
         x1="100"
         y1="0"
         x2="100"
         y2="300"
         id="line6" />
      <line
         x1="120"
         y1="0"
         x2="120"
         y2="300"
         id="line7" />
      <line
         x1="140"
         y1="0"
         x2="140"
         y2="300"
         id="line8" />
      <line
         x1="160"
         y1="0"
         x2="160"
         y2="300"
         id="line9" />
      <line
         x1="180"
         y1="0"
         x2="180"
         y2="300"
         id="line10" />
      <line
         x1="200"
         y1="0"
         x2="200"
         y2="300"
         id="line11" />
      <line
         x1="220"
         y1="0"
         x2="220"
         y2="300"
         id="line12" />
      <line
         x1="240"
         y1="0"
         x2="240"
         y2="300"
         id="line13" />
      <line
         x1="260"
         y1="0"
         x2="260"
         y2="300"
         id="line14" />
      <line
         x1="280"
         y1="0"
         x2="280"
         y2="300"
         id="line15" />
      <!-- Horizontal grid lines - finer resolution -->
      <line
         x1="0"
         y1="20"
         x2="300"
         y2="20"
         id="line16" />
      <line
         x1="0"
         y1="40"
         x2="300"
         y2="40"
         id="line17" />
      <line
         x1="0"
         y1="60"
         x2="300"
         y2="60"
         id="line18" />
      <line
         x1="0"
         y1="80"
         x2="300"
         y2="80"
         id="line19" />
      <line
         x1="0"
         y1="100"
         x2="300"
         y2="100"
         id="line20" />
      <line
         x1="0"
         y1="120"
         x2="300"
         y2="120"
         id="line21" />
      <line
         x1="0"
         y1="140"
         x2="300"
         y2="140"
         id="line22" />
      <line
         x1="0"
         y1="160"
         x2="300"
         y2="160"
         id="line23" />
      <line
         x1="0"
         y1="180"
         x2="300"
         y2="180"
         id="line24" />
      <line
         x1="0"
         y1="200"
         x2="300"
         y2="200"
         id="line25" />
      <line
         x1="0"
         y1="220"
         x2="300"
         y2="220"
         id="line26" />
      <line
         x1="0"
         y1="240"
         x2="300"
         y2="240"
         id="line27" />
      <line
         x1="0"
         y1="260"
         x2="300"
         y2="260"
         id="line28" />
      <line
         x1="0"
         y1="280"
         x2="300"
         y2="280"
         id="line29" />
    </g>
    <!-- Reference grid - coarser resolution -->
    <g
       stroke="#e63946"
       stroke-width="1.5"
       stroke-dasharray="8, 4"
       id="g35"
       sodipodi:insensitive="true">
      <!-- Reference grid area -->
      <rect
         x="50"
         y="40"
         width="200"
         height="200"
         fill="none"
         stroke="#e63946"
         stroke-width="3"
         stroke-dasharray="10, 5"
         id="rect29" />
      <!-- Reference grid lines - coarser resolution -->
      <line
         x1="50"
         y1="90"
         x2="250"
         y2="90"
         id="line30" />
      <line
         x1="50"
         y1="140"
         x2="250"
         y2="140"
         id="line31" />
      <line
         x1="50"
         y1="190"
         x2="250"
         y2="190"
         id="line32" />
      <line
         x1="100"
         y1="40"
         x2="100"
         y2="240"
         id="line33" />
      <line
         x1="150"
         y1="40"
         x2="150"
         y2="240"
         id="line34" />
      <line
         x1="200"
         y1="40"
         x2="200"
         y2="240"
         id="line35" />
    </g>
    <!-- Labels -->
    <text
       x="150"
       y="330"
       text-anchor="middle"
       font-family="Arial"
       font-size="16px"
       fill="#1d3557"
       id="text35"
       style="font-size:21.3333px"><tspan
         style="font-style:normal;font-variant:normal;font-weight:bold;font-stretch:normal;font-size:21.3333px;font-family:Arial;-inkscape-font-specification:'Arial Bold'"
         id="tspan47">Unaligned Two Image</tspan></text>
    <text
       x="78.578125"
       y="267.43945"
       font-family="Arial"
       font-size="12px"
       fill="#e63946"
       id="text36"
       style="font-size:16px"><tspan
         style="font-style:normal;font-variant:normal;font-weight:bold;font-stretch:normal;font-size:16px;font-family:Arial;-inkscape-font-specification:'Arial Bold'"
         id="tspan46">Reference Image</tspan></text>
    <text
       x="9.7480335"
       y="16.882812"
       font-family="Arial"
       font-size="12px"
       fill="#457b9d"
       id="text37"
       style="font-size:16px;fill:#0e68a2;fill-opacity:1"><tspan
         style="font-style:normal;font-variant:normal;font-weight:bold;font-stretch:normal;font-family:Arial;-inkscape-font-specification:'Arial Bold';fill:#0e68a2;fill-opacity:1;stroke:none;stroke-opacity:1"
         id="tspan45">Original Image</tspan></text>
  </g>
  <!-- Arrow between images -->
  <g
     transform="matrix(1.7463211,0,0,1,317.16914,221.5)"
     id="g38">
    <polygon
       points="0,15 40,15 40,5 60,20 40,35 40,25 0,25 "
       fill="#1d3557"
       id="polygon37" />
  </g>
  <!-- Right side - After alignment -->
  <rect
     x="431.13205"
     y="92"
     width="200"
     height="200"
     fill="#a8dadc"
     stroke="#457b9d"
     stroke-width="2"
     id="rect38"
     style="display:inline" />
  <g
     stroke="#457b9d"
     stroke-width="0.5"
     id="g43"
     transform="translate(431.13208,92)"
     style="display:inline">
    <!-- Vertical grid lines -->
    <line
       x1="50"
       y1="0"
       x2="50"
       y2="200"
       id="line38"
       style="fill:#284776;fill-opacity:1;stroke:#457b9d;stroke-width:1;stroke-dasharray:3,3;stroke-opacity:1;stroke-dashoffset:0" />
    <line
       x1="100"
       y1="0"
       x2="100"
       y2="200"
       id="line39"
       style="fill:#284776;fill-opacity:1;stroke:#457b9d;stroke-width:1;stroke-dasharray:3,3;stroke-opacity:1;stroke-dashoffset:0" />
    <line
       x1="150"
       y1="0"
       x2="150"
       y2="200"
       id="line40"
       style="fill:#284776;fill-opacity:1;stroke:#457b9d;stroke-width:1;stroke-dasharray:3,3;stroke-opacity:1;stroke-dashoffset:0" />
    <!-- Horizontal grid lines -->
    <line
       x1="0"
       y1="50"
       x2="200"
       y2="50"
       id="line41"
       style="fill:#284776;fill-opacity:1;stroke:#457b9d;stroke-width:1;stroke-dasharray:3,3;stroke-opacity:1;stroke-dashoffset:0" />
    <line
       x1="0"
       y1="100"
       x2="200"
       y2="100"
       id="line42"
       style="fill:#284776;fill-opacity:1;stroke:#457b9d;stroke-width:1;stroke-dasharray:3,3;stroke-opacity:1;stroke-dashoffset:0" />
    <line
       x1="0"
       y1="150"
       x2="200"
       y2="150"
       id="line43"
       style="fill:#284776;fill-opacity:1;stroke:#457b9d;stroke-width:1;stroke-dasharray:3,3;stroke-opacity:1;stroke-dashoffset:0" />
  </g>
  <rect
     x="431.13205"
     y="92"
     width="200"
     height="200"
     fill="none"
     stroke="#e63946"
     stroke-width="3"
     id="rect43"
     style="display:none;stroke:#e63946;stroke-opacity:1" />
  <g
     stroke="#e63946"
     stroke-width="1.5"
     stroke-dasharray="8, 4"
     id="g35-0"
     transform="translate(381.13208,52)"
     style="display:inline">
    <!-- Reference grid area -->
    <rect
       x="50"
       y="40"
       width="200"
       height="200"
       fill="none"
       stroke="#e63946"
       stroke-width="3"
       stroke-dasharray="10, 5"
       id="rect29-2"
       style="fill:none;fill-opacity:1;stroke:none;stroke-opacity:1" />
    <!-- Reference grid lines - coarser resolution -->
    <line
       x1="50"
       y1="90"
       x2="250"
       y2="90"
       id="line30-3"
       style="fill:none;fill-opacity:1;stroke:none;stroke-opacity:1" />
    <line
       x1="50"
       y1="140"
       x2="250"
       y2="140"
       id="line31-9"
       style="fill:none;fill-opacity:1;stroke:none;stroke-opacity:1" />
    <line
       x1="50"
       y1="190"
       x2="250"
       y2="190"
       id="line32-9"
       style="fill:none;fill-opacity:1;stroke:none;stroke-opacity:1" />
    <line
       x1="100"
       y1="40"
       x2="100"
       y2="240"
       id="line33-7"
       style="fill:none;fill-opacity:1;stroke:none;stroke-opacity:1" />
    <line
       x1="150"
       y1="40"
       x2="150"
       y2="240"
       id="line34-0"
       style="fill:none;fill-opacity:1;stroke:none;stroke-opacity:1" />
    <line
       x1="200"
       y1="40"
       x2="200"
       y2="240"
       id="line35-3"
       style="fill:none;fill-opacity:1;stroke:none;stroke-opacity:1" />
  </g>
  <text
     x="519.88208"
     y="386.13477"
     text-anchor="middle"
     font-family="Arial"
     font-size="16px"
     fill="#1d3557"
     id="text43"
     style="font-size:21.3333px"><tspan
       style="font-style:normal;font-variant:normal;font-weight:bold;font-stretch:normal;font-family:Arial;-inkscape-font-specification:'Arial Bold'"
       id="tspan48">Aligned Image</tspan></text>
  <text
     x="534.69489"
     y="323.8548"
     text-anchor="middle"
     font-family="Arial"
     font-size="12px"
     fill="#1d3557"
     id="text44"
     style="font-size:14.6667px"><tspan
       style="font-style:normal;font-variant:normal;font-weight:bold;font-stretch:normal;font-family:Arial;-inkscape-font-specification:'Arial Bold'"
       id="tspan49">Image Matches Reference Image</tspan></text>
  <!-- Title -->
  <text
     x="358.66913"
     y="32.25"
     text-anchor="middle"
     font-family="Arial"
     font-weight="bold"
     font-size="20px"
     fill="#1d3557"
     id="text45">Alignment to a Reference Raster</text>
  <text
     xml:space="preserve"
     style="font-style:normal;font-variant:normal;font-weight:bold;font-stretch:normal;font-size:16px;font-family:Arial;-inkscape-font-specification:'Arial Bold';text-align:center;letter-spacing:2px;writing-mode:lr-tb;direction:ltr;text-anchor:middle;fill:#284776;fill-opacity:1;stroke:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke-dasharray:none;stroke-opacity:1"
     x="369.63562"
     y="151.61523"
     id="text50"><tspan
       sodipodi:role="line"
       id="tspan50"
       x="369.63562"
       y="151.61523"
       style="font-size:16px;letter-spacing:2px;stroke:none" /><tspan
       sodipodi:role="line"
       x="369.63562"
       y="171.61523"
       id="tspan57"
       style="font-size:16px;letter-spacing:2px;stroke:none">CRS</tspan><tspan
       sodipodi:role="line"
       x="369.63562"
       y="191.61523"
       style="font-size:16px;letter-spacing:2px;stroke:none"
       id="tspan58">Resolution</tspan><tspan
       sodipodi:role="line"
       x="369.63562"
       y="211.61523"
       id="tspan56"
       style="font-size:16px;letter-spacing:2px;stroke:none">Bounds</tspan><tspan
       sodipodi:role="line"
       x="369.63562"
       y="231.61523"
       id="tspan53"
       style="font-size:16px;letter-spacing:2px;stroke:none" /></text>
</svg>
