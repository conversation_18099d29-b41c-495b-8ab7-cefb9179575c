
.bd-page-width {
  max-width: 100rem;
}
html {
    --pst-font-size-base: 17px;
}

table {
  width: auto; /* Override fit-content which breaks Styler user guide ipynb */
}

/* Main index page overview cards */

.intro-card {
  padding: 30px 10px 20px 10px;
}

.intro-card .sd-card-img-top {
  margin: 10px;
  height: 52px;
  background: none !important;
}

.intro-card .sd-card-title {
  /* color: var(--pst-color-info); */
  color: #912483;
  font-size: var(--pst-font-size-h5);
  padding: 1rem 0rem 0.5rem 0rem;
}

.intro-card .sd-card-footer {
  border: none !important;
}

.intro-card .sd-card-footer p.sd-card-text {
  max-width: 220px;
  margin-left: auto;
  margin-right: auto;
}

:root {
  --msg-box-min-width: 170px;
  --msg-box-max-width: 1fr;

}

/* .intro-card .sd-btn-secondary {
  background-color: #6c757d !important;
  border-color: #6c757d !important;
}

.intro-card .sd-btn-secondary:hover {
  background-color: #5a6268 !important;
  border-color: #545b62 !important;
} */

.card, .card img {
  background-color: var(--pst-color-background);
}
