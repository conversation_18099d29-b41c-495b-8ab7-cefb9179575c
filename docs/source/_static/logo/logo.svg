<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   width="1530pt"
   height="432pt"
   viewBox="0 0 1530 432"
   version="1.1"
   id="svg11"
   sodipodi:docname="logo_v1.svg"
   inkscape:version="1.3 (0e150ed, 2023-07-21)"
   inkscape:export-filename="logo.png"
   inkscape:export-xdpi="96"
   inkscape:export-ydpi="96"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:dc="http://purl.org/dc/elements/1.1/">
  <sodipodi:namedview
     id="namedview11"
     pagecolor="#ffffff"
     bordercolor="#999999"
     borderopacity="1"
     inkscape:showpageshadow="2"
     inkscape:pageopacity="0"
     inkscape:pagecheckerboard="0"
     inkscape:deskcolor="#d1d1d1"
     inkscape:document-units="pt"
     inkscape:zoom="0.32151388"
     inkscape:cx="772.90597"
     inkscape:cy="693.59369"
     inkscape:window-width="2560"
     inkscape:window-height="1387"
     inkscape:window-x="2560"
     inkscape:window-y="25"
     inkscape:window-maximized="0"
     inkscape:current-layer="svg11"
     showguides="true">
    <sodipodi:guide
       position="232.13026,416.7187"
       orientation="0,-1"
       id="guide13"
       inkscape:locked="false" />
    <sodipodi:guide
       position="226.75629,258.18727"
       orientation="1,0"
       id="guide14"
       inkscape:locked="false" />
    <sodipodi:guide
       position="231.03874,23.770918"
       orientation="0,-1"
       id="guide15"
       inkscape:locked="false" />
    <sodipodi:guide
       position="-122.61426,215.02977"
       orientation="0,-1"
       id="guide1"
       inkscape:locked="false" />
  </sodipodi:namedview>
  <metadata
     id="metadata1">
    <rdf:RDF>
      <cc:Work>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:date>2024-04-24T14:07:17.169076</dc:date>
        <dc:format>image/svg+xml</dc:format>
        <dc:creator>
          <cc:Agent>
            <dc:title>Matplotlib v3.8.0, https://matplotlib.org/</dc:title>
          </cc:Agent>
        </dc:creator>
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <defs
     id="defs1">
    <linearGradient
       id="linearGradient1"
       inkscape:collect="always">
      <stop
         style="stop-color:#aa67a7;stop-opacity:1;"
         offset="0.00146843"
         id="stop1" />
      <stop
         style="stop-color:#64a3bc;stop-opacity:1;"
         offset="0.29221734"
         id="stop3" />
      <stop
         style="stop-color:#a89124;stop-opacity:1;"
         offset="0.54185021"
         id="stop2" />
      <stop
         style="stop-color:#64a3bc;stop-opacity:1;"
         offset="0.77826726"
         id="stop4" />
      <stop
         style="stop-color:#aa67a7;stop-opacity:1;"
         offset="1"
         id="stop5" />
    </linearGradient>
    <style
       type="text/css"
       id="style1">*{stroke-linejoin: round; stroke-linecap: butt}</style>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter162"
       x="-0.046053238"
       y="-0.046053238"
       width="1.1112953"
       height="1.1112953">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood161" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="6.000000"
         id="feGaussianBlur161" />
      <feOffset
         result="offset"
         in="blur"
         dx="6.000000"
         dy="6.000000"
         id="feOffset161" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite161" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite162" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter164"
       x="-0.12732366"
       y="-0.12732366"
       width="1.3076988"
       height="1.3076988">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood162" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="6.000000"
         id="feGaussianBlur162" />
      <feOffset
         result="offset"
         in="blur"
         dx="6.000000"
         dy="6.000000"
         id="feOffset162" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite163" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite164" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter169"
       x="-0.011781262"
       y="-0.011781262"
       width="1.027625"
       height="1.027625">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood168" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="1.000000"
         id="feGaussianBlur168" />
      <feOffset
         result="offset"
         in="blur"
         dx="1.000000"
         dy="1.000000"
         id="feOffset168" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite168" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite169" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter174"
       x="-0.040083375"
       y="-0.040083375"
       width="1.0968682"
       height="1.0968682">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood173" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="3.000000"
         id="feGaussianBlur173" />
      <feOffset
         result="offset"
         in="blur"
         dx="3.000000"
         dy="3.000000"
         id="feOffset173" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite173" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite174" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter179"
       x="-0.062272385"
       y="-0.062272385"
       width="1.146018"
       height="1.146018">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood178" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="1.000000"
         id="feGaussianBlur178" />
      <feOffset
         result="offset"
         in="blur"
         dx="1.000000"
         dy="1.000000"
         id="feOffset178" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite178" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite179" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter105"
       x="-0.070535071"
       y="-0.070535071"
       width="1.1653926"
       height="1.1653926">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood103" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="1.000000"
         id="feGaussianBlur103" />
      <feOffset
         result="offset"
         in="blur"
         dx="1.000000"
         dy="1.000000"
         id="feOffset103" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite104" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite105" />
    </filter>
    <clipPath
       id="p7d126f1e7e">
      <rect
         x="55.080002"
         y="51.84"
         width="332.64001"
         height="332.64001"
         id="rect11-1" />
    </clipPath>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter89"
       x="-0.13377641"
       y="-0.13377641"
       width="1.323293"
       height="1.323293">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood87" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="6.000000"
         id="feGaussianBlur87" />
      <feOffset
         result="offset"
         in="blur"
         dx="6.000000"
         dy="6.000000"
         id="feOffset87" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite88" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite89" />
    </filter>
    <clipPath
       id="clipPath1">
      <rect
         x="55.080002"
         y="51.84"
         width="332.64001"
         height="332.64001"
         id="rect1" />
    </clipPath>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter97"
       x="-0.039540938"
       y="-0.039540938"
       width="1.0905649"
       height="1.0905649">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood96" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="2.8695237"
         id="feGaussianBlur96" />
      <feOffset
         result="offset"
         in="blur"
         dx="2.000000"
         dy="2.000000"
         id="feOffset96" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite96" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite97" />
    </filter>
    <clipPath
       id="clipPath2">
      <rect
         x="55.080002"
         y="51.84"
         width="332.64001"
         height="332.64001"
         id="rect2" />
    </clipPath>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter103"
       x="-0.0099709886"
       y="-0.0099709885"
       width="1.0240966"
       height="1.0240966">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood102" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="1.000000"
         id="feGaussianBlur102" />
      <feOffset
         result="offset"
         in="blur"
         dx="1.000000"
         dy="1.000000"
         id="feOffset102" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite102" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite103" />
    </filter>
    <clipPath
       id="clipPath3">
      <rect
         x="55.080002"
         y="51.84"
         width="332.64001"
         height="332.64001"
         id="rect3" />
    </clipPath>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter87"
       x="-0.046870987"
       y="-0.046870987"
       width="1.1132716"
       height="1.1132716">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood86" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="6.000000"
         id="feGaussianBlur86" />
      <feOffset
         result="offset"
         in="blur"
         dx="6.000000"
         dy="6.000000"
         id="feOffset86" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite86" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite87" />
    </filter>
    <clipPath
       id="clipPath4">
      <rect
         x="55.080002"
         y="51.84"
         width="332.64001"
         height="332.64001"
         id="rect4" />
    </clipPath>
    <clipPath
       id="clipPath5">
      <rect
         x="55.080002"
         y="51.84"
         width="332.64001"
         height="332.64001"
         id="rect5" />
    </clipPath>
    <clipPath
       id="clipPath6">
      <rect
         x="55.080002"
         y="51.84"
         width="332.64001"
         height="332.64001"
         id="rect6" />
    </clipPath>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter15"
       x="-0.076336888"
       y="-0.076290686"
       width="1.1690404"
       height="1.1554293">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood14" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="6.000000"
         id="feGaussianBlur14" />
      <feOffset
         result="offset"
         in="blur"
         dx="6.000000"
         dy="1.000000"
         id="feOffset14" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite14" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite15" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter17"
       x="-0.25215928"
       y="-0.25112573"
       width="1.5584921"
       height="1.5115849">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood15" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="6.000000"
         id="feGaussianBlur15" />
      <feOffset
         result="offset"
         in="blur"
         dx="6.000000"
         dy="1.000000"
         id="feOffset15" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite16" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite17" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter21"
       x="-0.05697148"
       y="-0.056673424"
       width="1.117539"
       height="1.1170652">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood20" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="1.000000"
         id="feGaussianBlur20" />
      <feOffset
         result="offset"
         in="blur"
         dx="1.000000"
         dy="1.000000"
         id="feOffset20" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite20" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite21" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter26"
       x="-0.10010402"
       y="-0.10004702"
       width="1.2130508"
       height="1.2130586">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood25" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="2.500000"
         id="feGaussianBlur25" />
      <feOffset
         result="offset"
         in="blur"
         dx="2.500000"
         dy="2.500000"
         id="feOffset25" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite25" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite26" />
    </filter>
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient1"
       id="linearGradient7"
       x1="532.19775"
       y1="-96.629425"
       x2="1593.476"
       y2="-96.629425"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(0.94133877,0,0,1.0623168,3.2619314,318.20715)" />
    <filter
       style="color-interpolation-filters:sRGB;"
       inkscape:label="Drop Shadow"
       id="filter10"
       x="-0.5985491"
       y="-0.61953949"
       width="2.2339526"
       height="2.2772258">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood9" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="1.000000"
         id="feGaussianBlur9" />
      <feOffset
         result="offset"
         in="blur"
         dx="1.000000"
         dy="1.000000"
         id="feOffset9" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite9" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite10" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter105-5"
       x="-0.070535071"
       y="-0.070535071"
       width="1.1653926"
       height="1.1653926">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood103-7" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="1.000000"
         id="feGaussianBlur103-4" />
      <feOffset
         result="offset"
         in="blur"
         dx="1.000000"
         dy="1.000000"
         id="feOffset103-4" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite104-4" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite105-7" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter17-3"
       x="-0.25215928"
       y="-0.25112573"
       width="1.5584921"
       height="1.5115849">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood15-3" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="6.000000"
         id="feGaussianBlur15-8" />
      <feOffset
         result="offset"
         in="blur"
         dx="6.000000"
         dy="1.000000"
         id="feOffset15-2" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite16-7" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite17-6" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter26-7"
       x="-0.10010402"
       y="-0.10004702"
       width="1.2130508"
       height="1.2130586">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood25-7" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="2.500000"
         id="feGaussianBlur25-6" />
      <feOffset
         result="offset"
         in="blur"
         dx="2.500000"
         dy="2.500000"
         id="feOffset25-4" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite25-1" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite26-4" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter21-6"
       x="-0.05697148"
       y="-0.056673424"
       width="1.117539"
       height="1.1170652">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood20-7" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="1.000000"
         id="feGaussianBlur20-0" />
      <feOffset
         result="offset"
         in="blur"
         dx="1.000000"
         dy="1.000000"
         id="feOffset20-9" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite20-5" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite21-7" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter10-0"
       x="-0.5985491"
       y="-0.61953949"
       width="2.2339526"
       height="2.2772258">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood9-8" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="1.000000"
         id="feGaussianBlur9-1" />
      <feOffset
         result="offset"
         in="blur"
         dx="1.000000"
         dy="1.000000"
         id="feOffset9-0" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite9-9" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite10-8" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter15-1"
       x="-0.076336888"
       y="-0.076290686"
       width="1.1690404"
       height="1.1554293">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood14-7" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="6.000000"
         id="feGaussianBlur14-0" />
      <feOffset
         result="offset"
         in="blur"
         dx="6.000000"
         dy="1.000000"
         id="feOffset14-2" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite14-6" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite15-4" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter105-5-4"
       x="-0.070535071"
       y="-0.070535071"
       width="1.1653926"
       height="1.1653926">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood103-7-0" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="1.000000"
         id="feGaussianBlur103-4-0" />
      <feOffset
         result="offset"
         in="blur"
         dx="1.000000"
         dy="1.000000"
         id="feOffset103-4-6" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite104-4-6" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite105-7-1" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter17-32"
       x="-0.45513585"
       y="-1.0127864"
       width="2.0077382"
       height="3.0615243">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood15-5" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="6.000000"
         id="feGaussianBlur15-85" />
      <feOffset
         result="offset"
         in="blur"
         dx="6.000000"
         dy="1.000000"
         id="feOffset15-0" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite16-5" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite17-67" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter17-1"
       x="-1.8068061"
       y="-0.43177129"
       width="5.0018228"
       height="1.8789531">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood15-0" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="6.000000"
         id="feGaussianBlur15-8-5" />
      <feOffset
         result="offset"
         in="blur"
         dx="6.000000"
         dy="1.000000"
         id="feOffset15-7" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite16-9" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite17-4" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter17-1-7"
       x="-1.5556095"
       y="-0.46624085"
       width="4.4447947"
       height="1.9491225">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood15-0-4" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="6.000000"
         id="feGaussianBlur15-8-8" />
      <feOffset
         result="offset"
         in="blur"
         dx="6.000000"
         dy="1.000000"
         id="feOffset15-7-3" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite16-9-8" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite17-4-1" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter26-2"
       x="-0.55226624"
       y="-0.15087199"
       width="2.1765071"
       height="1.3209702">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood25-3" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="2.500000"
         id="feGaussianBlur25-9" />
      <feOffset
         result="offset"
         in="blur"
         dx="2.500000"
         dy="2.500000"
         id="feOffset25-9" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite25-4" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite26-6" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter26-2-0"
       x="-0.55827802"
       y="-0.14719164"
       width="2.1897024"
       height="1.3131405">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood25-3-6" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="2.500000"
         id="feGaussianBlur25-9-1" />
      <feOffset
         result="offset"
         in="blur"
         dx="2.500000"
         dy="2.500000"
         id="feOffset25-9-5" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite25-4-4" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite26-6-6" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter26-2-0-6"
       x="-0.56130428"
       y="-0.14779502"
       width="2.1960891"
       height="1.3144242">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood25-3-6-4" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="2.500000"
         id="feGaussianBlur25-9-1-1" />
      <feOffset
         result="offset"
         in="blur"
         dx="2.500000"
         dy="2.500000"
         id="feOffset25-9-5-1" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite25-4-4-0" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite26-6-6-6" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter21-3"
       x="-0.30125731"
       y="-0.079893637"
       width="1.6213156"
       height="1.164775">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood20-2" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="1.000000"
         id="feGaussianBlur20-5" />
      <feOffset
         result="offset"
         in="blur"
         dx="1.000000"
         dy="1.000000"
         id="feOffset20-9-7" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite20-9" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite21-0" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter21-3-3"
       x="-0.31053058"
       y="-0.080047571"
       width="1.6404416"
       height="1.1650925">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood20-2-6" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="1.000000"
         id="feGaussianBlur20-5-6" />
      <feOffset
         result="offset"
         in="blur"
         dx="1.000000"
         dy="1.000000"
         id="feOffset20-9-3" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite20-9-7" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite21-0-3" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter21-3-3-0"
       x="-0.30105832"
       y="-0.079291898"
       width="1.6209053"
       height="1.163534">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood20-2-6-1" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="1.000000"
         id="feGaussianBlur20-5-6-6" />
      <feOffset
         result="offset"
         in="blur"
         dx="1.000000"
         dy="1.000000"
         id="feOffset20-9-3-5" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite20-9-7-9" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite21-0-3-5" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter10-5"
       x="-0.5985491"
       y="-0.61953949"
       width="2.2339526"
       height="2.2772258">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood9-3" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="1.000000"
         id="feGaussianBlur9-5" />
      <feOffset
         result="offset"
         in="blur"
         dx="1.000000"
         dy="1.000000"
         id="feOffset9-6" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite9-3" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite10-2" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:label="Drop Shadow"
       id="filter78"
       x="-0.071500202"
       y="-0.071500202"
       width="1.1430004"
       height="1.1430004">
      <feFlood
         result="flood"
         in="SourceGraphic"
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         id="feFlood77" />
      <feGaussianBlur
         result="blur"
         in="SourceGraphic"
         stdDeviation="5.000000"
         id="feGaussianBlur77" />
      <feOffset
         result="offset"
         in="blur"
         dx="0.000000"
         dy="0.000000"
         id="feOffset77" />
      <feComposite
         result="comp1"
         operator="in"
         in="flood"
         in2="offset"
         id="feComposite77" />
      <feComposite
         result="comp2"
         operator="over"
         in="SourceGraphic"
         in2="comp1"
         id="feComposite78" />
    </filter>
  </defs>
  <defs
     id="defs11">
    <clipPath
       id="pe4b23107bf">
      <rect
         x="55.080002"
         y="51.84"
         width="332.64001"
         height="332.64001"
         id="rect11" />
    </clipPath>
  </defs>
  <g
     id="g7"
     sodipodi:insensitive="true"
     style="display:none">
    <g
       id="patch_3"
       style="fill:#ab69a8;fill-opacity:1;stroke:none;stroke-linecap:butt;stroke-linejoin:round;filter:url(#filter89)"
       transform="matrix(1.2819365,0,0,1.2856707,-57.344774,-67.770899)">
      <path
         d="m 275.22115,218.16 c 0,-7.06773 -1.39219,-14.06673 -4.09689,-20.59646 -2.7047,-6.52973 -6.66932,-12.4632 -11.66696,-17.46084 -4.99764,-4.99764 -10.93111,-8.96226 -17.46084,-11.66696 -6.52973,-2.7047 -13.52873,-4.09689 -20.59646,-4.09689 -7.06773,0 -14.06673,1.39219 -20.59646,4.09689 -6.52973,2.7047 -12.4632,6.66932 -17.46084,11.66696 -4.99764,4.99764 -8.96226,10.93111 -11.66696,17.46084 -2.7047,6.52973 -4.09689,13.52873 -4.09689,20.59646 0,7.06773 1.39219,14.06673 4.09689,20.59646 2.7047,6.52973 6.66932,12.4632 11.66696,17.46084 4.99764,4.99764 10.93111,8.96226 17.46084,11.66696 6.52973,2.7047 13.52873,4.09689 20.59646,4.09689 7.06773,0 14.06673,-1.39219 20.59646,-4.09689 6.52973,-2.7047 12.4632,-6.66932 17.46084,-11.66696 4.99764,-4.99764 8.96226,-10.93111 11.66696,-17.46084 2.7047,-6.52973 4.09689,-13.52873 4.09689,-20.59646 m -20.55715,0 c 0,4.36819 -0.86044,8.6939 -2.53207,12.72958 -1.67164,4.03568 -4.12195,7.70284 -7.21073,10.79162 -3.08878,3.08878 -6.75594,5.53909 -10.79162,7.21073 -4.03568,1.67163 -8.36139,2.53207 -12.72958,2.53207 -4.36819,0 -8.6939,-0.86044 -12.72958,-2.53207 -4.03568,-1.67164 -7.70284,-4.12195 -10.79162,-7.21073 -3.08878,-3.08878 -5.5391,-6.75594 -7.21073,-10.79162 -1.67163,-4.03568 -2.53207,-8.36139 -2.53207,-12.72958 0,-4.36819 0.86044,-8.6939 2.53207,-12.72958 1.67163,-4.03568 4.12195,-7.70284 7.21073,-10.79162 3.08878,-3.08878 6.75594,-5.5391 10.79162,-7.21073 4.03568,-1.67163 8.36139,-2.53207 12.72958,-2.53207 4.36819,0 8.6939,0.86044 12.72958,2.53207 4.03568,1.67163 7.70284,4.12195 10.79162,7.21073 3.08878,3.08878 5.53909,6.75594 7.21073,10.79162 1.67163,4.03568 2.53207,8.36139 2.53207,12.72958 z"
         clip-path="url(#p7d126f1e7e)"
         style="fill:#ab69a8;fill-opacity:1;stroke:none;stroke-linecap:butt;stroke-linejoin:miter"
         id="path3" />
    </g>
    <g
       id="patch_4"
       style="fill:#bab85e;fill-opacity:1;stroke:none;stroke-linecap:butt;stroke-linejoin:round;filter:url(#filter97)"
       transform="matrix(1.2819365,0,0,1.2856707,-57.344774,-67.770899)">
      <path
         d="m 308.48515,218.16 c 0,-11.43592 -2.25262,-22.76064 -6.62896,-33.32605 -4.37634,-10.56541 -10.79127,-20.16604 -18.87769,-28.25245 -8.08641,-8.08642 -17.68705,-14.50135 -28.25246,-18.87769 -10.5654,-4.37634 -21.89012,-6.62896 -33.32604,-6.62896 -11.43592,0 -22.76064,2.25262 -33.32605,6.62896 -10.56541,4.37634 -20.16604,10.79127 -28.25245,18.87769 -8.08642,8.08641 -14.50135,17.68704 -18.87769,28.25245 -4.37634,10.56541 -6.62896,21.89013 -6.62896,33.32605 0,11.43592 2.25262,22.76063 6.62896,33.32604 4.37634,10.56541 10.79127,20.16605 18.87769,28.25246 8.08641,8.08642 17.68704,14.50135 28.25245,18.87769 10.56541,4.37634 21.89013,6.62896 33.32605,6.62896 11.43592,0 22.76064,-2.25262 33.32604,-6.62896 10.56541,-4.37634 20.16605,-10.79127 28.25246,-18.87769 8.08642,-8.08641 14.50135,-17.68705 18.87769,-28.25246 4.37634,-10.56541 6.62896,-21.89012 6.62896,-33.32604 m -20.55715,0 c 0,8.73638 -1.72088,17.3878 -5.06414,25.45916 -3.34327,8.07136 -8.24391,15.40569 -14.42146,21.58324 -6.17755,6.17755 -13.51188,11.07819 -21.58324,14.42146 -8.07136,3.34326 -16.72278,5.06414 -25.45916,5.06414 -8.73638,0 -17.3878,-1.72088 -25.45916,-5.06414 -8.07136,-3.34327 -15.40569,-8.24391 -21.58324,-14.42146 -6.17755,-6.17755 -11.07819,-13.51188 -14.42146,-21.58324 -3.34326,-8.07136 -5.06414,-16.72278 -5.06414,-25.45916 0,-8.73638 1.72088,-17.3878 5.06414,-25.45916 3.34327,-8.07136 8.24391,-15.40569 14.42146,-21.58324 6.17755,-6.17755 13.51188,-11.07819 21.58324,-14.42146 8.07136,-3.34326 16.72278,-5.06414 25.45916,-5.06414 8.73638,0 17.3878,1.72088 25.45916,5.06414 8.07136,3.34327 15.40569,8.24391 21.58324,14.42146 6.17755,6.17755 11.07819,13.51188 14.42146,21.58324 3.34326,8.07136 5.06414,16.72278 5.06414,25.45916 z"
         clip-path="url(#p7d126f1e7e)"
         style="fill:#bab85e;fill-opacity:1;stroke:none;stroke-linecap:butt;stroke-linejoin:miter"
         id="path4" />
    </g>
    <g
       id="patch_5"
       style="fill:#74bfdf;fill-opacity:1;stroke:none;stroke-linecap:butt;stroke-linejoin:round;filter:url(#filter103)"
       transform="matrix(1.2819365,0,0,1.2856707,-57.344774,-67.770899)">
      <path
         d="m 341.74915,218.16 c 0,-15.80411 -3.11306,-31.45454 -9.16103,-46.05563 -6.04797,-14.60109 -14.91323,-27.86888 -26.08842,-39.04407 -11.17519,-11.17519 -24.44298,-20.04045 -39.04407,-26.08842 -14.60109,-6.04797 -30.25152,-9.161032 -46.05563,-9.161032 -15.80411,0 -31.45454,3.113062 -46.05563,9.161032 -14.60109,6.04797 -27.86888,14.91323 -39.04407,26.08842 -11.17519,11.17519 -20.04045,24.44298 -26.08842,39.04407 -6.04797,14.60109 -9.16103,30.25152 -9.16103,46.05563 0,15.80411 3.11306,31.45454 9.16103,46.05563 6.04797,14.60109 14.91323,27.86888 26.08842,39.04407 11.17519,11.17519 24.44298,20.04045 39.04407,26.08842 14.60109,6.04797 30.25152,9.16103 46.05563,9.16103 15.80411,0 31.45454,-3.11306 46.05563,-9.16103 14.60109,-6.04797 27.86888,-14.91323 39.04407,-26.08842 11.17519,-11.17519 20.04045,-24.44298 26.08842,-39.04407 6.04797,-14.60109 9.16103,-30.25152 9.16103,-46.05563 m -20.55715,0 c 0,13.10456 -2.58131,26.0817 -7.59621,38.18874 -5.0149,12.10704 -12.36586,23.10853 -21.63219,32.37486 -9.26633,9.26633 -20.26782,16.61729 -32.37485,21.63219 -12.10705,5.0149 -25.08418,7.59621 -38.18875,7.59621 -13.10457,0 -26.08171,-2.58131 -38.18875,-7.59621 -12.10704,-5.0149 -23.10852,-12.36586 -32.37485,-21.63219 -9.26633,-9.26633 -16.61729,-20.26782 -21.63219,-32.37486 -5.0149,-12.10704 -7.59621,-25.08418 -7.59621,-38.18874 0,-13.10457 2.58131,-26.08171 7.59621,-38.18874 5.0149,-12.10705 12.36586,-23.10853 21.63219,-32.37486 9.26633,-9.26633 20.26781,-16.61729 32.37485,-21.63219 12.10704,-5.0149 25.08418,-7.59621 38.18875,-7.59621 13.10457,0 26.0817,2.58131 38.18875,7.59621 12.10703,5.0149 23.10852,12.36586 32.37485,21.63219 9.26633,9.26633 16.61729,20.26781 21.63219,32.37486 5.0149,12.10703 7.59621,25.08417 7.59621,38.18874 z"
         clip-path="url(#p7d126f1e7e)"
         style="fill:#74bfdf;fill-opacity:1;stroke:none;stroke-linecap:butt;stroke-linejoin:miter"
         id="path5" />
    </g>
    <g
       id="patch_6"
       style="fill:#aa67a7;fill-opacity:1;stroke:none;stroke-linecap:butt;stroke-linejoin:round;filter:url(#filter87)"
       transform="matrix(1.2819365,0,0,1.2856707,-57.344774,-67.770899)">
      <path
         d="m 375.01315,218.16 c 0,-20.17229 -3.9735,-40.14844 -11.6931,-58.78521 C 355.60044,140.73802 344.28487,123.80307 330.0209,109.5391 315.75693,95.275132 298.82198,83.959556 280.18521,76.239953 261.54844,68.52035 241.57229,64.546848 221.4,64.546848 c -20.17229,0 -40.14844,3.973502 -58.78521,11.693105 -18.63677,7.719603 -35.57172,19.035179 -49.83569,33.299147 -14.263968,14.26397 -25.579544,31.19892 -33.299147,49.83569 -7.719603,18.63677 -11.693105,38.61292 -11.693105,58.78521 0,20.17229 3.973502,40.14844 11.693105,58.78521 7.719603,18.63677 19.035179,35.57172 33.299147,49.83569 14.26397,14.26397 31.19892,25.57954 49.83569,33.29915 18.63677,7.7196 38.61292,11.6931 58.78521,11.6931 20.17229,0 40.14844,-3.9735 58.78521,-11.6931 18.63677,-7.71961 35.57172,-19.03518 49.83569,-33.29915 14.26397,-14.26397 25.57954,-31.19892 33.29915,-49.83569 7.7196,-18.63677 11.6931,-38.61292 11.6931,-58.78521 m -20.55715,0 c 0,17.47275 -3.44175,34.77561 -10.12829,50.91833 -6.68653,16.14272 -16.48781,30.81137 -28.84291,43.16647 -12.3551,12.3551 -27.02375,22.15638 -43.16647,28.84291 -16.14272,6.68654 -33.44558,10.12829 -50.91833,10.12829 -17.47275,0 -34.77561,-3.44175 -50.91833,-10.12829 C 154.33895,334.40118 139.6703,324.5999 127.3152,312.2448 114.9601,299.8897 105.15882,285.22105 98.472285,269.07833 91.785752,252.93561 88.344,235.63275 88.344,218.16 c 0,-17.47275 3.441752,-34.77561 10.128285,-50.91833 C 105.15882,151.09895 114.9601,136.4303 127.3152,124.0752 139.6703,111.7201 154.33895,101.91882 170.48167,95.232285 186.62439,88.545752 203.92725,85.104 221.4,85.104 c 17.47275,0 34.77561,3.441752 50.91833,10.128285 16.14272,6.686535 30.81137,16.487815 43.16647,28.842915 12.3551,12.3551 22.15638,27.02375 28.84291,43.16647 6.68654,16.14272 10.12829,33.44558 10.12829,50.91833 z"
         clip-path="url(#p7d126f1e7e)"
         style="fill:#aa67a7;fill-opacity:1;stroke:none;stroke-linecap:butt;stroke-linejoin:miter"
         id="path6" />
    </g>
    <g
       id="patch_10"
       style="stroke-linecap:butt;stroke-linejoin:round"
       transform="matrix(1.2819365,0,0,1.2856707,-57.344774,-67.770899)">
      <path
         d="m 508.06915,218.16 c 0,-37.64505 -7.41525,-74.92405 -21.82139,-109.70354 C 471.84163,73.676975 450.72477,42.073367 424.1057,15.454299 397.48663,-11.16477 365.88302,-32.281626 331.10354,-46.687762 296.32405,-61.093898 259.04505,-68.509152 221.4,-68.509152 c -37.64505,0 -74.92405,7.415254 -109.70353,21.82139 -34.779495,14.406136 -66.383103,35.522992 -93.002171,62.142061 -26.619069,26.619068 -47.735925,58.222676 -62.142061,93.002161 -14.406136,34.77949 -21.82139,72.05849 -21.82139,109.70354 0,37.64505 7.415254,74.92405 21.82139,109.70354 14.406136,34.77949 35.522992,66.38309 62.142061,93.00216 26.619068,26.61907 58.222676,47.73593 93.002171,62.14206 34.77948,14.40614 72.05848,21.82139 109.70353,21.82139 37.64505,0 74.92405,-7.41525 109.70354,-21.82139 34.77948,-14.40613 66.38309,-35.52299 93.00216,-62.14206 26.61907,-26.61907 47.73593,-58.22267 62.14206,-93.00216 14.40614,-34.77949 21.82139,-72.05849 21.82139,-109.70354 m -20.55715,0 c 0,34.94551 -6.8835,69.55121 -20.25657,101.83665 -13.37307,32.28544 -32.97563,61.62274 -57.68583,86.33295 -24.71021,24.7102 -54.04751,44.31276 -86.33295,57.68583 C 290.95121,477.3885 256.34551,484.272 221.4,484.272 c -34.94551,0 -69.55121,-6.8835 -101.83665,-20.25657 C 87.277907,450.64236 57.940606,431.0398 33.2304,406.3296 8.520195,381.61939 -11.082363,352.28209 -24.45543,319.99665 -37.828497,287.71121 -44.712,253.10551 -44.712,218.16 c 0,-34.94551 6.883503,-69.55121 20.25657,-101.83665 C -11.082363,84.037907 8.520195,54.700606 33.2304,29.9904 57.940606,5.280195 87.277907,-14.322363 119.56335,-27.69543 151.84879,-41.068497 186.45449,-47.952 221.4,-47.952 c 34.94551,0 69.55121,6.883503 101.83665,20.25657 32.28544,13.373067 61.62274,32.975625 86.33295,57.68583 24.7102,24.710206 44.31276,54.047507 57.68583,86.33295 C 480.6285,148.60879 487.512,183.21449 487.512,218.16 Z"
         clip-path="url(#p7d126f1e7e)"
         style="fill:#e1bd17;stroke:#e1bd17;stroke-linecap:butt;stroke-linejoin:miter"
         id="path10" />
    </g>
    <g
       id="patch_11"
       style="stroke-linecap:butt;stroke-linejoin:round"
       transform="matrix(1.2819365,0,0,1.2856707,-57.344774,-67.770899)">
      <path
         d="m 541.33315,218.16 c 0,-42.01324 -8.27569,-83.61795 -24.35346,-122.433117 C 500.90192,56.911714 477.33475,21.640943 447.6269,-8.066901 417.91906,-37.774746 382.64829,-61.341921 343.83312,-77.419691 305.01795,-93.49746 263.41324,-101.77315 221.4,-101.77315 c -42.01324,0 -83.61795,8.27569 -122.433117,24.353459 -38.815169,16.07777 -74.08594,39.644945 -103.793784,69.35279 -29.707845,29.707844 -53.27502,64.978615 -69.35279,103.793784 C -90.25746,134.54205 -98.533152,176.14676 -98.533152,218.16 c 0,42.01324 8.275692,83.61795 24.353461,122.43312 16.07777,38.81517 39.644945,74.08594 69.35279,103.79378 29.707844,29.70785 64.978615,53.27502 103.793784,69.35279 38.815167,16.07777 80.419877,24.35346 122.433117,24.35346 42.01324,0 83.61795,-8.27569 122.43312,-24.35346 38.81517,-16.07777 74.08594,-39.64494 103.79378,-69.35279 29.70785,-29.70784 53.27502,-64.97861 69.35279,-103.79378 16.07777,-38.81517 24.35346,-80.41988 24.35346,-122.43312 m -20.55715,0 c 0,39.3137 -7.74394,78.24512 -22.78864,114.56623 -15.0447,36.32112 -37.09758,69.32559 -64.89656,97.12457 -27.79898,27.79898 -60.80345,49.85186 -97.12457,64.89656 C 299.64512,509.79206 260.7137,517.536 221.4,517.536 c -39.3137,0 -78.24512,-7.74394 -114.56623,-22.78864 C 70.512646,479.70266 37.508181,457.64978 9.7092,429.8508 -18.089781,402.05182 -40.142659,369.04735 -55.187359,332.72623 -70.232059,296.40512 -77.976,257.4737 -77.976,218.16 c 0,-39.3137 7.743941,-78.24512 22.788641,-114.56623 C -40.142659,67.272646 -18.089781,34.268181 9.7092,6.4692 37.508181,-21.329781 70.512646,-43.382659 106.83377,-58.427359 143.15488,-73.472059 182.0863,-81.216 221.4,-81.216 c 39.3137,0 78.24512,7.743941 114.56623,22.788641 36.32112,15.0447 69.32559,37.097578 97.12457,64.896559 27.79898,27.798981 49.85186,60.803446 64.89656,97.12457 15.0447,36.32111 22.78864,75.25253 22.78864,114.56623 z"
         clip-path="url(#p7d126f1e7e)"
         style="fill:#74bfdf;stroke:#74bfdf;stroke-linecap:butt;stroke-linejoin:miter"
         id="path11" />
    </g>
  </g>
  <path
     d="m 596.20752,156.2495 h -60.99865 v 44.12693 h 50.98862 q 7.09044,0 10.53138,3.64782 3.54523,3.53016 3.54523,9.53142 0,6.00126 -3.54523,9.53142 -3.54521,3.53015 -10.53138,3.53015 h -50.98862 v 56.83549 q 0,10.82581 -4.37939,16.12104 -4.27511,5.17756 -11.05274,5.17756 -6.8819,0 -11.26129,-5.29523 -4.27511,-5.29524 -4.27511,-16.00337 v -132.7338 q 0,-7.531 1.98115,-12.23787 1.98115,-4.82455 6.15199,-6.94264 4.27512,-2.23577 10.84421,-2.23577 h 72.98983 q 7.40326,0 10.94848,3.7655 3.64949,3.64783 3.64949,9.64909 0,6.11893 -3.64949,9.88443 -3.54522,3.64783 -10.94848,3.64783 z m 102.29003,130.38037 q -10.32285,9.06073 -20.02007,13.64993 -9.59295,4.47153 -21.58413,4.47153 -10.94848,0 -19.29017,-4.82455 -8.23743,-4.94221 -12.72109,-13.29691 -4.48367,-8.3547 -4.48367,-18.12146 0,-13.17924 7.40327,-22.47532 7.40325,-9.29607 20.33288,-12.47321 2.71104,-0.70603 13.45098,-3.17714 10.73993,-2.47111 18.35173,-4.47153 7.71607,-2.11809 16.68339,-5.05989 -0.52135,-12.70855 -4.58793,-18.59214 -3.96231,-6.00127 -16.57912,-6.00127 -10.8442,0 -16.37058,3.41249 -5.4221,3.41248 -9.3844,10.23744 -3.85804,6.82497 -5.52638,9.06074 -1.56407,2.11809 -6.8819,2.11809 -4.79648,0 -8.34169,-3.41248 -3.44095,-3.53016 -3.44095,-8.94307 0,-8.47237 5.31783,-16.47404 5.31783,-8.00169 16.57912,-13.17925 11.26128,-5.17756 28.04895,-5.17756 18.76882,0 29.50875,5.05989 10.73993,4.94222 15.11932,15.76802 4.48366,10.82581 4.48366,28.71192 0,11.2965 -0.10427,19.18051 0,7.88401 -0.10427,17.5331 0,9.06073 2.60677,18.94516 2.71106,9.76676 2.71106,12.59089 0,4.94221 -4.17085,9.06073 -4.06657,4.00084 -9.28014,4.00084 -4.37939,0 -8.65451,-4.5892 -4.27512,-4.70688 -9.07159,-13.53226 z m -1.87688,-46.48037 q -6.25627,2.58878 -18.24746,5.53058 -11.88692,2.82412 -16.47485,4.23618 -4.58793,1.29439 -8.75878,5.29523 -4.17085,3.88317 -4.17085,10.94348 0,7.29565 4.90075,12.47321 4.90075,5.05989 12.82536,5.05989 8.44596,0 15.53641,-4.11851 7.19471,-4.23619 10.53139,-10.82581 3.85803,-7.29565 3.85803,-24.00505 z m 82.68704,-48.24544 v 3.88317 q 7.50753,-11.17882 16.37058,-16.35639 8.96733,-5.29523 20.54143,-5.29523 11.26129,0 20.12433,5.53058 8.86305,5.53057 13.24244,15.65035 2.81533,5.88359 3.6495,12.70856 0.83417,6.82496 0.83417,17.41543 v 59.77728 q 0,9.64908 -3.96231,14.5913 -3.85804,4.94222 -10.1143,4.94222 -6.36055,0 -10.32285,-5.05989 -3.96231,-5.05989 -3.96231,-14.47363 v -53.54068 q 0,-15.88569 -3.9623,-24.2404 -3.85804,-8.47237 -15.53641,-8.47237 -7.6118,0 -13.86807,5.17757 -6.25627,5.05989 -9.17587,14.00294 -2.08542,7.17798 -2.08542,26.82918 v 40.24376 q 0,9.76676 -4.06657,14.70897 -3.96231,4.82455 -10.32286,4.82455 -6.15199,0 -10.1143,-5.05989 -3.96231,-5.05989 -3.96231,-14.47363 v -92.84306 q 0,-9.17841 3.54523,-13.64993 3.54522,-4.58921 9.69722,-4.58921 3.75376,0 6.77763,2.00043 3.02386,2.00041 4.79647,6.00126 1.87688,4.00084 1.87688,9.76676 z m 106.35662,91.54867 V 147.54178 q 0,-10.59046 4.27512,-15.88569 4.27511,-5.29524 11.05274,-5.29524 6.98617,0 11.26129,5.29524 4.37939,5.17756 4.37939,15.88569 v 135.91095 q 0,10.70813 -4.37939,16.00337 -4.27512,5.29523 -11.26129,5.29523 -6.67335,0 -11.05274,-5.29523 -4.27512,-5.41291 -4.27512,-16.00337 z m 89.46465,-91.54867 v 3.88317 q 7.50755,-11.17882 16.37063,-16.35639 8.96729,-5.29523 20.54139,-5.29523 11.2613,0 20.1244,5.53058 8.863,5.53057 13.2424,15.65035 2.8153,5.88359 3.6495,12.70856 0.8341,6.82496 0.8341,17.41543 v 59.77728 q 0,9.64908 -3.9623,14.5913 -3.8579,4.94222 -10.1143,4.94222 -6.3605,0 -10.3228,-5.05989 -3.9623,-5.05989 -3.9623,-14.47363 v -53.54068 q 0,-15.88569 -3.9623,-24.2404 -3.858,-8.47237 -15.5364,-8.47237 -7.61183,0 -13.86806,5.17757 -6.25632,5.05989 -9.17589,14.00294 -2.08544,7.17798 -2.08544,26.82918 v 40.24376 q 0,9.76676 -4.06658,14.70897 -3.96228,4.82455 -10.32282,4.82455 -6.15202,0 -10.11431,-5.05989 -3.96228,-5.05989 -3.96228,-14.47363 v -92.84306 q 0,-9.17841 3.54518,-13.64993 3.54527,-4.58921 9.6972,-4.58921 3.75378,0 6.77764,2.00043 3.02386,2.00041 4.7965,6.00126 1.87684,4.00084 1.87684,9.76676 z m 221.26352,58.12987 q 0,15.65036 -7.1947,28.12357 -7.0905,12.47321 -20.8542,19.53352 -13.7638,7.06031 -32.6369,7.06031 -22.6269,0 -37.3291,-9.64909 -10.4271,-6.94264 -16.9962,-18.47448 -6.4649,-11.64951 -6.4649,-22.59298 0,-6.35428 3.8581,-10.82581 3.9623,-4.5892 10.01,-4.5892 4.9008,0 8.2375,3.53015 3.441,3.53016 5.8391,10.47279 2.9197,8.23703 6.2563,13.76761 3.441,5.53057 9.5929,9.1784 6.1521,3.53015 16.1621,3.53015 13.7639,0 22.314,-7.17798 8.6545,-7.29565 8.6545,-18.12146 0,-8.59004 -4.6922,-13.88527 -4.5879,-5.41291 -11.9911,-8.23703 -7.299,-2.82412 -19.603,-6.00126 -16.4749,-4.35386 -27.6319,-10.11978 -11.0527,-5.88358 -17.6218,-15.88569 -6.4648,-10.11978 -6.4648,-25.0641 0,-14.23828 6.8818,-25.29944 6.882,-11.06115 19.9158,-16.94473 13.0339,-6.00128 30.6558,-6.00128 14.0766,0 24.2952,4.00085 10.3228,3.88317 17.1004,10.4728 6.7777,6.47194 9.9058,13.64993 3.1282,7.17798 3.1282,14.00294 0,6.2366 -3.9624,11.29649 -3.858,4.94222 -9.6972,4.94222 -5.3178,0 -8.1331,-2.9418 -2.7111,-3.05946 -5.9435,-9.88443 -4.1709,-9.76676 -10.01,-15.17966 -5.8392,-5.53058 -18.7689,-5.53058 -11.9911,0 -19.3944,6.00126 -7.2989,5.8836 -7.2989,14.2383 0,5.17755 2.5024,8.94305 2.5026,3.76549 6.882,6.47195 4.3794,2.70645 8.863,4.23619 4.4837,1.52972 14.8065,4.47152 12.9296,3.41248 23.3567,7.531 10.5315,4.11852 17.8304,10.0021 7.4033,5.8836 11.4699,14.94433 4.1708,8.94305 4.1708,22.00462 z m 124.0827,32.24208 -7.299,-21.65161 h -62.1456 l -7.299,22.1223 q -4.2751,12.9439 -7.2989,17.5331 -3.0239,4.47153 -9.9058,4.47153 -5.8393,0 -10.3229,-4.82455 -4.4836,-4.82454 -4.4836,-10.94348 0,-3.53015 1.0427,-7.29565 1.0427,-3.7655 3.4409,-10.47279 l 39.1018,-112.02356 q 1.6683,-4.82455 3.9622,-11.53184 2.3983,-6.82496 5.005,-11.2965 2.7111,-4.47153 6.9862,-7.17798 4.3794,-2.82413 10.7399,-2.82413 6.4648,0 10.7399,2.82413 4.3794,2.70645 6.9863,7.06031 2.711,4.35386 4.4836,9.41375 1.8769,4.94222 4.6922,13.29691 l 39.9359,111.31754 q 4.6922,12.70855 4.6922,18.47447 0,6.00126 -4.4837,11.06115 -4.3794,4.94222 -10.6356,4.94222 -3.6495,0 -6.2564,-1.52974 -2.6067,-1.41206 -4.3793,-3.88317 -1.7726,-2.58878 -3.858,-7.76634 -1.9812,-5.29523 -3.441,-9.29607 z m -61.3115,-47.89243 h 45.6709 l -23.044,-71.19145 z m 158.7008,-6.35427 h -10.8442 v 55.42342 q 0,10.94348 -4.2751,16.12104 -4.2752,5.17756 -11.157,5.17756 -7.4033,0 -11.4699,-5.41291 -4.0666,-5.4129 -4.0666,-15.88569 v -132.7338 q 0,-11.2965 4.4837,-16.35639 4.4836,-5.05989 14.4937,-5.05989 h 50.363 q 10.4271,0 17.8303,1.05905 7.4033,0.94137 13.3467,4.00084 7.1948,3.41248 12.7212,9.76676 5.5263,6.35428 8.3417,14.82665 2.9195,8.3547 2.9195,17.76844 0,19.29819 -9.6972,30.83002 -9.5929,11.53184 -29.1959,16.35638 8.2374,4.94222 15.7449,14.59131 7.5076,9.64909 13.3467,20.59256 5.9435,10.82581 9.1759,19.6512 3.3367,8.70771 3.3367,12.00252 0,3.41248 -1.9812,6.82497 -1.8768,3.29481 -5.2135,5.17756 -3.3367,2.00042 -7.7161,2.00042 -5.2135,0 -8.7588,-2.82413 -3.5452,-2.70645 -6.1519,-6.94263 -2.5026,-4.23619 -6.882,-12.47322 l -12.4082,-23.29901 q -6.6734,-12.82623 -11.9912,-19.53353 -5.2136,-6.70729 -10.6357,-9.1784 -5.4221,-2.4711 -13.6595,-2.4711 z m 17.7261,-72.48584 h -28.5703 v 47.42174 h 27.7362 q 11.1569,0 18.7687,-2.11809 7.6118,-2.23576 11.5742,-7.41333 4.0665,-5.29523 4.0665,-14.47363 0,-7.17799 -3.2324,-12.59088 -3.2324,-5.53058 -8.9673,-8.23703 -5.4221,-2.58878 -21.3756,-2.58878 z"
     id="text1-7"
     style="font-size:226.855px;line-height:1.25;font-family:'Arial Rounded MT Bold';-inkscape-font-specification:'Arial Rounded MT Bold, ';fill:url(#linearGradient7);stroke-width:1.18154"
     aria-label="FanInSAR" />
  <g
     id="g1">
    <ellipse
       style="display:inline;fill:#cccccc;fill-opacity:0.2;stroke:#b2b2b2;stroke-width:3;stroke-linecap:round;stroke-linejoin:bevel;stroke-dasharray:none;stroke-dashoffset:25;stroke-opacity:0.2;paint-order:normal"
       id="path12"
       cx="226.21512"
       cy="216.02992"
       rx="215.02991"
       ry="212.67488" />
    <g
       id="patch_2"
       style="display:inline;stroke-linecap:butt;stroke-linejoin:round;filter:url(#filter105-5-4)"
       transform="matrix(1.2819365,0,0,1.2856707,-65.214537,-71.839609)" />
    <g
       id="g70"
       transform="translate(13.436393,-1.0730496)"
       style="display:inline;stroke-linecap:butt;stroke-linejoin:round">
      <g
         id="g65"
         style="stroke-linecap:butt;stroke-linejoin:round">
        <path
           style="fill:none;fill-opacity:1;stroke:#aa67a7;stroke-width:27.236;stroke-linecap:round;stroke-linejoin:bevel;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;paint-order:markers fill stroke;filter:url(#filter17-32)"
           id="path1"
           sodipodi:type="arc"
           sodipodi:cx="290.49826"
           sodipodi:cy="109.85059"
           sodipodi:rx="55.524574"
           sodipodi:ry="55.700558"
           sodipodi:start="0.52745909"
           sodipodi:end="1.8179066"
           sodipodi:open="true"
           sodipodi:arc-type="arc"
           d="m 338.47642,137.88688 a 55.524574,55.700558 0 0 1 -61.55964,25.97227"
           transform="rotate(22.5,-18.681165,-31.656612)" />
      </g>
      <path
         style="display:inline;fill:none;fill-opacity:1;stroke:#aa67a7;stroke-width:27.236;stroke-linecap:round;stroke-linejoin:bevel;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;paint-order:markers fill stroke;filter:url(#filter17-1)"
         id="path1-7"
         sodipodi:type="arc"
         sodipodi:cx="290.49826"
         sodipodi:cy="109.85059"
         sodipodi:rx="55.524574"
         sodipodi:ry="55.700558"
         sodipodi:start="2.650105"
         sodipodi:end="3.9073129"
         sodipodi:open="true"
         sodipodi:arc-type="arc"
         d="m 241.54603,136.1378 a 55.524574,55.700558 0 0 1 8.92549,-64.890834"
         transform="rotate(22.5,-18.681165,-31.656612)" />
      <path
         style="display:inline;fill:none;fill-opacity:1;stroke:#aa67a7;stroke-width:27.236;stroke-linecap:round;stroke-linejoin:bevel;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;paint-order:markers fill stroke;filter:url(#filter17-1-7)"
         id="path1-7-2"
         sodipodi:type="arc"
         sodipodi:cx="290.49826"
         sodipodi:cy="109.85059"
         sodipodi:rx="55.524574"
         sodipodi:ry="55.700558"
         sodipodi:start="2.792881"
         sodipodi:end="3.9704772"
         sodipodi:open="true"
         sodipodi:arc-type="arc"
         d="M 238.3155,128.88277 A 55.524574,55.700558 0 0 1 252.98039,68.789361"
         transform="rotate(142.5,233.27453,150.608)" />
    </g>
    <g
       id="g68"
       transform="translate(13.436393,-1.0730496)"
       style="display:inline;stroke-linecap:butt;stroke-linejoin:round">
      <path
         style="display:inline;fill:none;fill-opacity:1;stroke:#bab85e;stroke-width:27.236;stroke-linecap:round;stroke-linejoin:bevel;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;paint-order:markers fill stroke;filter:url(#filter26-2)"
         id="path1-9-7"
         sodipodi:type="arc"
         sodipodi:cx="290.50308"
         sodipodi:cy="109.86732"
         sodipodi:rx="97.913406"
         sodipodi:ry="97.866615"
         sodipodi:start="2.548745"
         sodipodi:end="4.0203208"
         sodipodi:open="true"
         sodipodi:arc-type="arc"
         d="M 209.29831,164.54784 A 97.913406,97.866615 0 0 1 228.02151,34.51708"
         transform="rotate(22.5,-18.681165,-31.656612)" />
      <path
         style="display:inline;fill:none;fill-opacity:1;stroke:#bab85e;stroke-width:27.236;stroke-linecap:round;stroke-linejoin:bevel;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;paint-order:markers fill stroke;filter:url(#filter26-2-0)"
         id="path1-9-7-4"
         sodipodi:type="arc"
         sodipodi:cx="290.50308"
         sodipodi:cy="109.86732"
         sodipodi:rx="97.913406"
         sodipodi:ry="97.866615"
         sodipodi:start="2.5038576"
         sodipodi:end="4.0149618"
         sodipodi:open="true"
         sodipodi:arc-type="arc"
         d="M 211.83492,168.13487 A 97.913406,97.866615 0 0 1 227.61842,34.852839"
         transform="rotate(142.5,233.44011,150.29321)" />
      <path
         style="display:inline;fill:none;fill-opacity:1;stroke:#bab85e;stroke-width:27.236;stroke-linecap:round;stroke-linejoin:bevel;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;paint-order:markers fill stroke;filter:url(#filter26-2-0-6)"
         id="path1-9-7-4-9"
         sodipodi:type="arc"
         sodipodi:cx="290.50308"
         sodipodi:cy="109.86732"
         sodipodi:rx="97.913406"
         sodipodi:ry="97.866615"
         sodipodi:start="2.5087887"
         sodipodi:end="4.0124884"
         sodipodi:open="true"
         sodipodi:arc-type="arc"
         d="M 211.54841,167.74643 A 97.913406,97.866615 0 0 1 227.43298,35.008533"
         transform="rotate(-97.5,298.60537,197.67272)" />
    </g>
    <g
       id="g69"
       transform="translate(13.436393,-1.0730496)"
       style="display:inline;stroke-linecap:butt;stroke-linejoin:round">
      <path
         style="display:inline;fill:none;fill-opacity:1;stroke:#73bedd;stroke-width:27.236;stroke-linecap:round;stroke-linejoin:bevel;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;paint-order:markers fill stroke;filter:url(#filter21-3)"
         id="path1-9-9-2-2"
         transform="matrix(0.91342775,0.40700092,-0.40701129,0.91342313,-13.53648,4.739256)"
         sodipodi:type="arc"
         sodipodi:cx="293.29953"
         sodipodi:cy="101.77171"
         sodipodi:rx="140.49036"
         sodipodi:ry="140.94046"
         sodipodi:start="2.4482192"
         sodipodi:end="4.0416963"
         sodipodi:open="true"
         sodipodi:arc-type="arc"
         d="M 185.2492,191.85174 A 140.49036,140.94046 0 0 1 153.55903,87.229317 140.49036,140.94046 0 0 1 205.98073,-8.6398217" />
      <path
         style="display:inline;fill:none;fill-opacity:1;stroke:#73bedd;stroke-width:27.236;stroke-linecap:round;stroke-linejoin:bevel;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;paint-order:markers fill stroke;filter:url(#filter21-3-3)"
         id="path1-9-9-2-2-3"
         transform="matrix(-0.80918701,0.58755118,-0.58754199,-0.80919368,509.68334,129.08711)"
         sodipodi:type="arc"
         sodipodi:cx="293.29953"
         sodipodi:cy="101.77171"
         sodipodi:rx="140.49036"
         sodipodi:ry="140.94046"
         sodipodi:start="2.4399127"
         sodipodi:end="4.0271841"
         sodipodi:open="true"
         sodipodi:arc-type="arc"
         d="M 185.99878,192.74902 A 140.49036,140.94046 0 0 1 153.40274,88.829685 140.49036,140.94046 0 0 1 204.39278,-7.3569923" />
      <path
         style="display:inline;fill:none;fill-opacity:1;stroke:#73bedd;stroke-width:27.236;stroke-linecap:round;stroke-linejoin:bevel;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;paint-order:markers fill stroke;filter:url(#filter21-3-3-0)"
         id="path1-9-9-2-2-3-8"
         transform="matrix(-0.10424074,-0.9945521,0.99455328,-0.10422945,141.07504,521.15426)"
         sodipodi:type="arc"
         sodipodi:cx="293.29953"
         sodipodi:cy="101.77171"
         sodipodi:rx="140.49036"
         sodipodi:ry="140.94046"
         sodipodi:start="2.4343603"
         sodipodi:end="4.0420155"
         sodipodi:open="true"
         sodipodi:arc-type="arc"
         d="M 186.50396,193.3453 A 140.49036,140.94046 0 0 1 153.4641,88.178696 140.49036,140.94046 0 0 1 206.01587,-8.6677776" />
    </g>
    <ellipse
       style="display:inline;fill:none;fill-opacity:1;stroke:#73bedd;stroke-width:27.6818;stroke-linecap:round;stroke-linejoin:bevel;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;paint-order:markers fill stroke;filter:url(#filter10-5)"
       id="path7"
       cx="290.50214"
       cy="109.49855"
       rx="13.566891"
       ry="13.107236"
       transform="rotate(22.5,-9.2656799,1.5815469)" />
    <circle
       style="display:inline;fill:none;fill-opacity:0.736842;stroke:#aa67a7;stroke-width:29.0465;stroke-linecap:butt;stroke-linejoin:round;stroke-dasharray:none;stroke-opacity:1;filter:url(#filter78)"
       id="path67"
       cx="226.38559"
       cy="216.00002"
       r="185.47675" />
  </g>
</svg>
