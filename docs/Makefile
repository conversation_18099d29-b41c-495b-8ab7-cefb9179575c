# Minimal makefile for Sphinx documentation
#

# You can set these variables from the command line, and also
# from the environment for the first two.
SPHINXOPTS    ?=
SPHINXBUILD   ?= sphinx-build
SOURCEDIR     = source
BUILDDIR      = build

# Put it first so that "make" without argument is like "make help".
help:
	@$(SPHINXBUILD) -M help "$(SOURCEDIR)" "$(BUILDDIR)" $(SPHINXOPTS) $(O)

# clean generated files
clean-gen:
	@api_dirs=$$(find $(SOURCEDIR) -type d -name generated); \
	for dir in $$api_dirs; do \
		rm -rf $$dir; echo "Cleaned up $$dir"; \
	done

# clean all build files and generated files
clean:
	@rm -rf $(BUILDDIR)/*
	@echo "Cleaned up $(BUILDDIR)"

	@$(MAKE) clean-gen








.PHONY: help Makefile

# Catch-all target: route all unknown targets to Sphinx using the new
# "make mode" option.  $(O) is meant as a shortcut for $(SPHINXOPTS).
%: Makefile
	@$(<PERSON>HINXBUILD) -M $@ "$(SOURCEDIR)" "$(BUILDDIR)" $(SPHINXOPTS) $(O)
